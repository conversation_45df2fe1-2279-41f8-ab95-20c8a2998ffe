#!/usr/bin/env python3
"""
Java 项目文档生成脚本

使用方法:
    python generate_java_doc.py <java_project_path> [--llm]

示例:
    # 使用模板生成
    python generate_java_doc.py /path/to/your/java/project

    # 使用 LLM 生成
    python generate_java_doc.py /path/to/your/java/project --llm

    # 使用自定义 LLM 配置
    python generate_java_doc.py /path/to/your/java/project --llm --api-base https://api.openai.com/v1 --api-key your_key --model gpt-4
"""

import sys
import os
import argparse
from pathlib import Path
from generator import ACModGenerator
from llm_config import LLMConfig


def find_java_modules(root_path: Path):
    """
    查找 Java 项目根目录下的所有模块

    Args:
        root_path: 项目根目录

    Returns:
        list: 模块路径列表
    """
    modules = []

    # 检查根目录本身是否是模块
    if list(root_path.rglob("*.java")):
        modules.append(root_path)

    # 查找子目录中的模块
    for item in root_path.iterdir():
        if item.is_dir() and not item.name.startswith('.'):
            # 检查是否包含 Java 文件
            java_files = list(item.rglob("*.java"))
            if java_files:
                # 检查是否是独立模块（有 pom.xml 或 build.gradle）
                if (item / "pom.xml").exists() or (item / "build.gradle").exists() or (item / "build.gradle.kts").exists():
                    modules.append(item)
                # 或者是包含 src 目录的模块
                elif (item / "src").exists():
                    modules.append(item)
                # 或者直接包含 Java 包结构
                elif any(java_file.parent.name != item.name for java_file in java_files[:5]):
                    modules.append(item)

    return modules


def generate_single_module_doc(module_path: Path, root_path: Path, llm_config=None):
    """
    为单个模块生成文档

    Args:
        module_path: 模块路径
        root_path: 项目根路径
        llm_config: LLM 配置（可选）

    Returns:
        bool: 生成是否成功
    """
    try:
        # 创建文档生成器
        generator = ACModGenerator(root_path=root_path, llm_config=llm_config)

        # 生成文档
        success = generator.generate_documentation(
            module_path=module_path,
            force=True,  # 强制覆盖现有文档
            enable_context_limiting=True  # 启用上下文限制
        )

        return success

    except Exception as e:
        print(f"   ❌ 错误: {e}")
        return False


def generate_java_documentation(project_path_str: str, llm_config=None):
    """
    为指定的 Java 项目生成 .ac.mod.md 文档

    Args:
        project_path_str: Java 项目路径字符串
        llm_config: LLM 配置（可选）

    Returns:
        bool: 生成是否成功
    """
    # 转换为 Path 对象并解析绝对路径
    project_path = Path(project_path_str).resolve()

    print(f"🚀 开始为 Java 项目生成文档")
    print(f"📂 项目路径: {project_path}")

    # 验证路径
    if not project_path.exists():
        print(f"❌ 错误: 路径不存在 - {project_path}")
        return False

    if not project_path.is_dir():
        print(f"❌ 错误: 不是目录 - {project_path}")
        return False

    # 查找所有 Java 模块
    print("🔍 扫描 Java 模块...")
    modules = find_java_modules(project_path)

    if not modules:
        print("❌ 错误: 没有找到任何 Java 模块")
        return False

    print(f"✅ 找到 {len(modules)} 个 Java 模块:")
    for i, module in enumerate(modules, 1):
        rel_path = module.relative_to(project_path) if module != project_path else Path(".")
        java_count = len(list(module.rglob("*.java")))
        print(f"   {i}. {rel_path} ({java_count} Java 文件)")

    # 检查项目类型
    if (project_path / "pom.xml").exists():
        print("📦 检测到 Maven 多模块项目")
    elif (project_path / "build.gradle").exists() or (project_path / "build.gradle.kts").exists():
        print("📦 检测到 Gradle 多模块项目")
    else:
        print("📦 检测到 Java 项目")

    # 为每个模块生成文档
    print(f"\n📝 开始为 {len(modules)} 个模块生成文档...")
    success_count = 0
    failed_modules = []

    for i, module in enumerate(modules, 1):
        rel_path = module.relative_to(project_path) if module != project_path else Path(".")
        print(f"\n[{i}/{len(modules)}] 处理模块: {rel_path}")

        try:
            success = generate_single_module_doc(module, project_path, llm_config)
            if success:
                doc_path = module / ".ac.mod.md"
                print(f"   ✅ 文档已生成: {doc_path}")
                success_count += 1
            else:
                print(f"   ❌ 生成失败")
                failed_modules.append(str(rel_path))
        except Exception as e:
            print(f"   ❌ 错误: {e}")
            failed_modules.append(str(rel_path))

    # 输出总结
    print(f"\n� 生成完成:")
    print(f"   - 成功: {success_count}/{len(modules)} 个模块")
    print(f"   - 失败: {len(failed_modules)} 个模块")

    if failed_modules:
        print(f"   - 失败的模块: {', '.join(failed_modules)}")

    return success_count > 0


def create_llm_config(args):
    """根据命令行参数创建 LLM 配置"""
    if not args.llm:
        return None

    # 优先级: 命令行参数 > 环境变量 > 默认值
    api_key = args.api_key or os.getenv("OPENAI_API_KEY")

    # 对于 api_base 和 model，如果用户没有明确指定，则检查环境变量
    api_base = args.api_base
    if api_base == "https://api.openai.com/v1":  # 如果是默认值，检查环境变量
        api_base = os.getenv("OPENAI_API_BASE", api_base)

    model = args.model
    if model == "gpt-3.5-turbo":  # 如果是默认值，检查环境变量
        model = os.getenv("OPENAI_MODEL", model)

    if not api_key:
        print("⚠️  警告: 未提供 API 密钥，将使用模板生成")
        print("   可以通过 --api-key 参数或 OPENAI_API_KEY 环境变量设置")
        return None

    print(f"🤖 使用 LLM 生成模式:")
    print(f"   - API 地址: {api_base}")
    print(f"   - 模型: {model}")
    print(f"   - API 密钥: {api_key[:8]}...{api_key[-4:] if len(api_key) > 12 else '***'}")

    return LLMConfig(
        api_base=api_base,
        api_key=api_key,
        model=model
    )


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Java 项目文档生成脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  # 使用模板生成
  python generate_java_doc.py /path/to/java/project

  # 使用 LLM 生成（需要配置 API 密钥）
  python generate_java_doc.py /path/to/java/project --llm

  # 使用自定义 LLM 配置
  python generate_java_doc.py /path/to/java/project --llm \\
    --api-base https://api.openai.com/v1 \\
    --api-key your_api_key \\
    --model gpt-4

环境变量:
  OPENAI_API_KEY    - OpenAI API 密钥
  OPENAI_API_BASE   - OpenAI API 地址 (默认: https://api.openai.com/v1)
  OPENAI_MODEL      - 模型名称 (默认: gpt-3.5-turbo)
        """
    )

    parser.add_argument(
        "project_path",
        help="Java 项目根目录路径"
    )

    parser.add_argument(
        "--llm",
        action="store_true",
        help="启用 LLM 生成模式（需要 API 密钥）"
    )

    parser.add_argument(
        "--api-base",
        default="http://************:30804/qwen3-235b-fp8-openai-server/v1/v1",
        help="LLM API 地址 (默认: https://api.openai.com/v1)"
    )

    parser.add_argument(
        "--api-key",
        default="10298467",
        help="LLM API 密钥 (也可通过 OPENAI_API_KEY 环境变量设置)"
    )

    parser.add_argument(
        "--model",
        default="/Qwen2-235B-A22B-FP8",
        help="LLM 模型名称 (默认: gpt-3.5-turbo)"
    )

    args = parser.parse_args()

    # 创建 LLM 配置
    llm_config = create_llm_config(args)

    # 生成文档
    success = generate_java_documentation(args.project_path, llm_config)

    # 输出结果
    print("\n" + "=" * 50)
    if success:
        print("🎉 任务完成! Java 项目文档已成功生成")
        print("\n💡 提示:")
        print("   - 每个模块的文档保存为对应目录下的 .ac.mod.md 文件")
        print("   - 可以查看各个模块的文档了解项目结构")
    else:
        print("💥 任务失败!")
        sys.exit(1)


if __name__ == "__main__":
    main()
