#!/usr/bin/env python3
"""
Java 项目文档生成脚本

使用方法:
    python generate_java_doc.py <java_project_path>

示例:
    python generate_java_doc.py /path/to/your/java/project
    python generate_java_doc.py ./my-spring-boot-app
"""

import sys
import os
from pathlib import Path
from generator import ACModGenerator


def generate_java_documentation(project_path_str: str):
    """
    为指定的 Java 项目生成 .ac.mod.md 文档
    
    Args:
        project_path_str: Java 项目路径字符串
        
    Returns:
        bool: 生成是否成功
    """
    # 转换为 Path 对象并解析绝对路径
    project_path = Path(project_path_str).resolve()
    
    print(f"🚀 开始为 Java 项目生成文档")
    print(f"📂 项目路径: {project_path}")
    
    # 验证路径
    if not project_path.exists():
        print(f"❌ 错误: 路径不存在 - {project_path}")
        return False
    
    if not project_path.is_dir():
        print(f"❌ 错误: 不是目录 - {project_path}")
        return False
    
    # 检查是否包含 Java 文件
    java_files = list(project_path.rglob("*.java"))
    if not java_files:
        print(f"❌ 错误: 目录中没有找到 Java 文件")
        return False
    
    print(f"✅ 找到 {len(java_files)} 个 Java 文件")
    
    # 检查项目类型
    project_type = "Java 项目"
    if (project_path / "pom.xml").exists():
        project_type = "Maven 项目"
        print("📦 检测到 Maven 项目")
    elif (project_path / "build.gradle").exists() or (project_path / "build.gradle.kts").exists():
        project_type = "Gradle 项目"
        print("📦 检测到 Gradle 项目")
    
    try:
        # 创建文档生成器
        generator = ACModGenerator(root_path=project_path.parent)
        
        # 检查是否需要上下文限制
        print("🔍 分析项目规模...")
        context_result = generator.check_module_context_limits(project_path)
        
        print(f"📊 项目统计:")
        print(f"   - 总文件数: {context_result['total_files']}")
        print(f"   - 总 Token 数: {context_result['total_tokens']:,}")
        
        if context_result['exceeds_limit']:
            print(f"⚠️  项目较大，超过上下文限制")
            print(f"   - 推荐策略: {context_result['recommended_strategy']}")
            print(f"   - 将启用智能裁剪功能")
        
        # 生成文档
        print("\n📝 正在生成文档...")
        success = generator.generate_documentation(
            module_path=project_path,
            force=True,  # 强制覆盖现有文档
            enable_context_limiting=True  # 启用上下文限制
        )
        
        if success:
            doc_path = project_path / ".ac.mod.md"
            print(f"\n✅ 文档生成成功!")
            print(f"📄 文档位置: {doc_path}")
            
            # 显示文档基本信息
            if doc_path.exists():
                content = doc_path.read_text(encoding='utf-8')
                lines = content.split('\n')
                print(f"📊 文档信息:")
                print(f"   - 总行数: {len(lines)}")
                print(f"   - 总字符数: {len(content):,}")
                
                # 显示文档开头
                print(f"\n📖 文档开头预览:")
                print("-" * 50)
                for i, line in enumerate(lines[:5], 1):
                    print(f"{i:2d}: {line}")
                if len(lines) > 5:
                    print("   ...")
            
            return True
        else:
            print("❌ 文档生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 生成过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    # 检查命令行参数
    if len(sys.argv) != 2:
        print("Java 项目文档生成脚本")
        print("=" * 40)
        print("\n使用方法:")
        print(f"  python {sys.argv[0]} <java_project_path>")
        print("\n示例:")
        print(f"  python {sys.argv[0]} /home/<USER>/my-java-project")
        print(f"  python {sys.argv[0]} ./spring-boot-app")
        print(f"  python {sys.argv[0]} ../parent-project/module-name")
        sys.exit(1)
    
    # 获取项目路径
    java_project_path = sys.argv[1]
    
    # 生成文档
    success = generate_java_documentation(java_project_path)
    
    # 输出结果
    print("\n" + "=" * 50)
    if success:
        print("🎉 任务完成! Java 项目文档已成功生成")
    else:
        print("💥 任务失败!")
        sys.exit(1)


if __name__ == "__main__":
    main()
