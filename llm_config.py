"""
LLM Configuration for AC Module Generator

This module provides configuration and client setup for OpenAI-like API calls.
Supports custom API endpoints and API keys for various LLM providers.
"""

import os
import logging
from typing import Optional, Dict, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class LLMConfig:
    """Configuration for LLM API calls"""
    api_base: str = "https://api.openai.com/v1"  # Default OpenAI endpoint
    api_key: Optional[str] = None
    model: str = "gpt-3.5-turbo"
    max_tokens: int = 4000
    temperature: float = 0.1
    timeout: int = 60
    
    def __post_init__(self):
        """Initialize configuration from environment variables if not provided"""
        if self.api_key is None:
            self.api_key = os.getenv("OPENAI_API_KEY")
        
        # Allow override from environment variables
        self.api_base = os.getenv("OPENAI_API_BASE", self.api_base)
        self.model = os.getenv("OPENAI_MODEL", self.model)
        
        # Parse numeric values from environment
        if os.getenv("OPENAI_MAX_TOKENS"):
            try:
                self.max_tokens = int(os.getenv("OPENAI_MAX_TOKENS"))
            except ValueError:
                logger.warning("Invalid OPENAI_MAX_TOKENS value, using default")
        
        if os.getenv("OPENAI_TEMPERATURE"):
            try:
                self.temperature = float(os.getenv("OPENAI_TEMPERATURE"))
            except ValueError:
                logger.warning("Invalid OPENAI_TEMPERATURE value, using default")


class LLMClient:
    """
    OpenAI-like API client for LLM calls.
    
    Supports various providers that implement OpenAI-compatible APIs.
    """
    
    def __init__(self, config: Optional[LLMConfig] = None):
        """
        Initialize the LLM client.
        
        Args:
            config: LLM configuration. If None, uses default config.
        """
        self.config = config or LLMConfig()
        self._client = None
        
        if not self.config.api_key:
            logger.warning("No API key provided. LLM functionality will be disabled.")
    
    def _get_client(self):
        """Get or create the OpenAI client"""
        if self._client is None:
            try:
                import openai
                self._client = openai.OpenAI(
                    api_key=self.config.api_key,
                    base_url=self.config.api_base,
                    timeout=self.config.timeout
                )
            except ImportError:
                logger.error("OpenAI library not installed. Please install with: pip install openai")
                raise
            except Exception as e:
                logger.error(f"Failed to initialize OpenAI client: {e}")
                raise
        
        return self._client
    
    def chat_completion(self, messages: list, **kwargs) -> Optional[str]:
        """
        Send a chat completion request to the LLM.
        
        Args:
            messages: List of message dictionaries with 'role' and 'content'
            **kwargs: Additional parameters to override config defaults
            
        Returns:
            Generated text response or None if failed
        """
        if not self.config.api_key:
            logger.warning("No API key configured, skipping LLM call")
            return None
        
        try:
            client = self._get_client()
            
            # Merge config with kwargs
            params = {
                "model": kwargs.get("model", self.config.model),
                "messages": messages,
                "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
                "temperature": kwargs.get("temperature", self.config.temperature),
            }
            
            # Add any additional parameters
            for key, value in kwargs.items():
                if key not in params:
                    params[key] = value
            
            logger.debug(f"Sending chat completion request with model: {params['model']}")
            
            response = client.chat.completions.create(**params)
            
            if response.choices and len(response.choices) > 0:
                content = response.choices[0].message.content
                logger.debug(f"Received response with {len(content)} characters")
                return content
            else:
                logger.warning("No response choices returned from LLM")
                return None
                
        except Exception as e:
            logger.error(f"LLM API call failed: {e}")
            return None
    
    def generate_documentation(self, module_info: Dict[str, Any]) -> Optional[str]:
        """
        Generate documentation for a module using LLM.
        
        Args:
            module_info: Dictionary containing module analysis information
            
        Returns:
            Generated documentation content or None if failed
        """
        if not self.config.api_key:
            logger.info("No API key configured, using template-based generation")
            return None
        
        # Prepare the prompt for documentation generation
        prompt = self._create_documentation_prompt(module_info)
        
        messages = [
            {
                "role": "system",
                "content": "You are a technical documentation expert. Generate clear, comprehensive documentation for software modules in Chinese. Follow the .ac.mod.md format and include practical examples."
            },
            {
                "role": "user", 
                "content": prompt
            }
        ]
        
        return self.chat_completion(messages)
    
    def _create_documentation_prompt(self, module_info: Dict[str, Any]) -> str:
        """Create a prompt for documentation generation"""
        module_name = module_info.get("name", "Unknown Module")
        module_path = module_info.get("path", "")
        description = module_info.get("description", "")
        
        # Build the prompt with available information
        prompt_parts = [
            f"请为以下模块生成 .ac.mod.md 格式的技术文档：",
            f"",
            f"模块名称: {module_name}",
            f"模块路径: {module_path}",
        ]
        
        if description:
            prompt_parts.append(f"模块描述: {description}")
        
        # Add directory structure if available
        if "directory_structure" in module_info:
            prompt_parts.extend([
                f"",
                f"目录结构:",
                f"```",
                str(module_info["directory_structure"]),
                f"```"
            ])
        
        # Add core components if available
        if "core_components" in module_info and module_info["core_components"]:
            prompt_parts.extend([
                f"",
                f"核心组件:",
            ])
            for component in module_info["core_components"]:
                if isinstance(component, dict):
                    comp_name = component.get("name", "Unknown")
                    comp_type = component.get("type", "")
                    comp_desc = component.get("description", "")
                    prompt_parts.append(f"- {comp_name} ({comp_type}): {comp_desc}")
        
        # Add dependencies if available
        if "dependencies" in module_info and module_info["dependencies"]:
            prompt_parts.extend([
                f"",
                f"依赖关系:",
            ])
            for dep in module_info["dependencies"]:
                prompt_parts.append(f"- {dep}")
        
        prompt_parts.extend([
            f"",
            f"请生成包含以下部分的完整文档：",
            f"1. 模块概述和主要功能",
            f"2. 目录结构说明", 
            f"3. 快速开始和基本使用示例",
            f"4. 核心组件详解",
            f"5. 依赖关系说明",
            f"6. 测试命令",
            f"",
            f"请使用中文编写，代码注释使用英文，格式要清晰易读。"
        ])
        
        return "\n".join(prompt_parts)
    
    def is_available(self) -> bool:
        """Check if LLM client is available and configured"""
        return bool(self.config.api_key)


def create_llm_client(api_base: Optional[str] = None, api_key: Optional[str] = None, 
                     model: Optional[str] = None) -> LLMClient:
    """
    Create an LLM client with custom configuration.
    
    Args:
        api_base: Custom API endpoint URL
        api_key: API key for authentication
        model: Model name to use
        
    Returns:
        Configured LLM client
    """
    config = LLMConfig()
    
    if api_base:
        config.api_base = api_base
    if api_key:
        config.api_key = api_key
    if model:
        config.model = model
    
    return LLMClient(config)
