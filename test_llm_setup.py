#!/usr/bin/env python3
"""
Quick Test Script for LLM Setup

This script helps you verify that your LLM configuration is working correctly.
"""

import os
import sys
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from llm_config import LLMConfig, LLMClient


def test_openai_import():
    """Test if OpenAI library is available"""
    try:
        import openai
        print("✓ OpenAI library is installed")
        print(f"  Version: {openai.__version__}")
        return True
    except ImportError:
        print("✗ OpenAI library not found")
        print("  Install with: pip install openai")
        return False


def test_environment_variables():
    """Test environment variable configuration"""
    print("\n=== Environment Variables ===")
    
    env_vars = [
        "OPENAI_API_BASE",
        "OPENAI_API_KEY", 
        "OPENAI_MODEL",
        "OPENAI_MAX_TOKENS",
        "OPENAI_TEMPERATURE"
    ]
    
    found_vars = {}
    for var in env_vars:
        value = os.getenv(var)
        if value:
            # Mask API key for security
            if "KEY" in var and len(value) > 8:
                display_value = value[:4] + "..." + value[-4:]
            else:
                display_value = value
            print(f"✓ {var}={display_value}")
            found_vars[var] = value
        else:
            print(f"  {var}=<not set>")
    
    return found_vars


def test_llm_config():
    """Test LLM configuration"""
    print("\n=== LLM Configuration ===")
    
    try:
        config = LLMConfig()
        print(f"✓ LLM Config created successfully")
        print(f"  API Base: {config.api_base}")
        print(f"  Model: {config.model}")
        print(f"  Max Tokens: {config.max_tokens}")
        print(f"  Temperature: {config.temperature}")
        
        if config.api_key:
            print(f"  API Key: {config.api_key[:4]}...{config.api_key[-4:] if len(config.api_key) > 8 else '***'}")
        else:
            print("  API Key: <not set>")
        
        return config
    except Exception as e:
        print(f"✗ Failed to create LLM config: {e}")
        return None


def test_llm_client(config):
    """Test LLM client creation"""
    print("\n=== LLM Client ===")
    
    try:
        client = LLMClient(config)
        print("✓ LLM Client created successfully")
        
        if client.is_available():
            print("✓ LLM Client is available (API key configured)")
        else:
            print("⚠ LLM Client is not available (no API key)")
        
        return client
    except Exception as e:
        print(f"✗ Failed to create LLM client: {e}")
        return None


def test_simple_api_call(client):
    """Test a simple API call (if API key is available)"""
    print("\n=== API Call Test ===")
    
    if not client or not client.is_available():
        print("⚠ Skipping API call test (no API key configured)")
        return False
    
    print("Testing API call with simple prompt...")
    
    try:
        messages = [
            {"role": "user", "content": "Say 'Hello, AC Module Generator!' in Chinese"}
        ]
        
        response = client.chat_completion(messages, max_tokens=50)
        
        if response:
            print("✓ API call successful!")
            print(f"  Response: {response}")
            return True
        else:
            print("✗ API call failed (no response)")
            return False
            
    except Exception as e:
        print(f"✗ API call failed: {e}")
        return False


def test_documentation_generation(client):
    """Test documentation generation"""
    print("\n=== Documentation Generation Test ===")
    
    if not client or not client.is_available():
        print("⚠ Skipping documentation test (no API key configured)")
        return False
    
    module_info = {
        "name": "test_module",
        "path": "./test_module",
        "description": "A simple test module for demonstration",
        "core_components": [
            {"name": "TestClass", "type": "class", "description": "Main test class"}
        ]
    }
    
    try:
        doc = client.generate_documentation(module_info)
        
        if doc:
            print("✓ Documentation generation successful!")
            print(f"  Generated {len(doc)} characters")
            print("  Preview:")
            print("  " + "\n  ".join(doc[:200].split('\n')))
            if len(doc) > 200:
                print("  ...")
            return True
        else:
            print("✗ Documentation generation failed (no content)")
            return False
            
    except Exception as e:
        print(f"✗ Documentation generation failed: {e}")
        return False


def main():
    """Run all tests"""
    print("AC Module Generator - LLM Setup Test")
    print("=" * 50)
    
    # Test 1: OpenAI library
    openai_ok = test_openai_import()
    
    # Test 2: Environment variables
    env_vars = test_environment_variables()
    
    # Test 3: LLM configuration
    config = test_llm_config()
    
    # Test 4: LLM client
    client = test_llm_client(config) if config else None
    
    # Test 5: Simple API call (optional)
    api_ok = test_simple_api_call(client) if client else False
    
    # Test 6: Documentation generation (optional)
    doc_ok = test_documentation_generation(client) if client else False
    
    # Summary
    print("\n" + "=" * 50)
    print("Test Summary:")
    print(f"  OpenAI Library: {'✓' if openai_ok else '✗'}")
    print(f"  Environment Variables: {'✓' if env_vars else '⚠'}")
    print(f"  LLM Configuration: {'✓' if config else '✗'}")
    print(f"  LLM Client: {'✓' if client else '✗'}")
    print(f"  API Call: {'✓' if api_ok else '⚠'}")
    print(f"  Documentation Generation: {'✓' if doc_ok else '⚠'}")
    
    if not openai_ok:
        print("\nNext steps:")
        print("1. Install OpenAI library: pip install openai")
    
    if not env_vars.get("OPENAI_API_KEY"):
        print("\nTo enable LLM functionality:")
        print("1. Set your API key: export OPENAI_API_KEY='your_key_here'")
        print("2. Optionally set API base: export OPENAI_API_BASE='your_endpoint'")
        print("3. Optionally set model: export OPENAI_MODEL='your_model'")
    
    if config and client and not api_ok:
        print("\nAPI call failed. Check:")
        print("1. API key is valid")
        print("2. API endpoint is correct")
        print("3. Network connectivity")
        print("4. Model name is supported")


if __name__ == "__main__":
    main()
