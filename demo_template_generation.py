#!/usr/bin/env python3
"""
模板生成演示脚本

展示模板生成的完整过程，包括数据收集、模板渲染和最终输出。
"""

import tempfile
from pathlib import Path
from generator import ACModGenerator
from template_engine import TemplateEngine
from module_scanner import ModuleScanner


def create_demo_module():
    """创建一个演示模块"""
    temp_dir = tempfile.mkdtemp()
    demo_module = Path(temp_dir) / "demo_module"
    demo_module.mkdir()
    
    # 创建 __init__.py
    (demo_module / "__init__.py").write_text('''"""
Demo Module for Template Generation

这是一个演示模块，用于展示模板生成的工作原理。
"""

__version__ = "1.0.0"
__author__ = "AC Module Generator"

from .core import DemoClass
from .utils import helper_function

__all__ = ["DemoClass", "helper_function"]
''')
    
    # 创建核心类文件
    (demo_module / "core.py").write_text('''"""
Core functionality of the demo module
"""

class DemoClass:
    """
    Main demonstration class.
    
    This class shows how the template generator analyzes
    and documents Python classes.
    """
    
    def __init__(self, name: str):
        """Initialize the demo class."""
        self.name = name
    
    def greet(self) -> str:
        """Return a greeting message."""
        return f"Hello from {self.name}!"
    
    def process_data(self, data: list) -> dict:
        """
        Process input data and return results.
        
        Args:
            data: List of items to process
            
        Returns:
            Dictionary with processing results
        """
        return {
            "processed": len(data),
            "items": data
        }
''')
    
    # 创建工具函数文件
    (demo_module / "utils.py").write_text('''"""
Utility functions for the demo module
"""

def helper_function(value: str) -> str:
    """
    A helper function that processes strings.
    
    Args:
        value: Input string to process
        
    Returns:
        Processed string
    """
    return value.upper().strip()

def calculate_score(items: list) -> float:
    """Calculate average score from a list of numbers."""
    if not items:
        return 0.0
    return sum(items) / len(items)
''')
    
    # 创建示例文件
    (demo_module / "example.py").write_text('''#!/usr/bin/env python3
"""
Usage example for the demo module
"""

from demo_module import DemoClass, helper_function

def main():
    # 1. 创建实例
    demo = DemoClass("Demo")
    
    # 2. 基本使用
    message = demo.greet()
    print(message)
    
    # 3. 处理数据
    data = [1, 2, 3, 4, 5]
    result = demo.process_data(data)
    print(f"处理结果: {result}")
    
    # 4. 使用工具函数
    processed = helper_function("  hello world  ")
    print(f"处理后的字符串: {processed}")

if __name__ == "__main__":
    main()
''')
    
    # 创建测试文件
    (demo_module / "test_demo.py").write_text('''"""
Tests for the demo module
"""

import unittest
from demo_module import DemoClass, helper_function

class TestDemoModule(unittest.TestCase):
    
    def test_demo_class(self):
        demo = DemoClass("Test")
        self.assertEqual(demo.greet(), "Hello from Test!")
    
    def test_helper_function(self):
        result = helper_function("  test  ")
        self.assertEqual(result, "TEST")

if __name__ == "__main__":
    unittest.main()
''')
    
    return demo_module


def demonstrate_data_collection(module_path: Path):
    """演示数据收集过程"""
    print("=" * 60)
    print("1. 数据收集阶段")
    print("=" * 60)
    
    scanner = ModuleScanner(module_path.parent)
    
    # 收集基本信息
    print(f"模块名称: {module_path.name}")
    print(f"模块路径: {module_path}")
    
    # 分析目录结构
    print("\n目录结构分析:")
    structure = scanner.get_directory_structure(module_path)
    print(f"  发现文件: {len(structure.get('children', []))} 个")
    
    # 分析核心组件
    print("\n核心组件分析:")
    components = scanner.extract_core_components(module_path)
    print(f"  发现组件: {len(components)} 个")
    for comp in components:
        print(f"    - {comp.get('name')} ({comp.get('type')})")
    
    # 查找使用示例
    print("\n使用示例分析:")
    examples = scanner.find_usage_examples(module_path)
    print(f"  发现示例: {len(examples)} 个")
    
    # 查找测试命令
    print("\n测试命令分析:")
    test_commands = scanner.find_test_commands(module_path)
    print(f"  发现测试命令: {len(test_commands)} 个")
    
    return {
        "name": module_path.name,
        "path": str(module_path.relative_to(module_path.parent)),
        "description": scanner.extract_module_description(module_path),
        "directory_structure": structure,
        "core_components": components,
        "dependencies": scanner.find_dependencies(module_path),
        "usage_examples": examples,
        "test_commands": test_commands
    }


def demonstrate_template_rendering(analysis_data: dict):
    """演示模板渲染过程"""
    print("\n" + "=" * 60)
    print("2. 模板渲染阶段")
    print("=" * 60)
    
    engine = TemplateEngine()
    
    print("模板变量替换:")
    print(f"  {{{{MODULE_NAME}}}} -> {analysis_data.get('name')}")
    print(f"  {{{{MODULE_DESCRIPTION}}}} -> {analysis_data.get('description', '模块描述')[:50]}...")
    
    print("\n复杂部分渲染:")
    
    # 渲染目录结构
    structure = engine._render_directory_structure(analysis_data.get("directory_structure", {}))
    print(f"  目录结构: {len(structure.split(chr(10)))} 行")
    
    # 渲染核心组件
    components = engine._render_core_components(analysis_data.get("core_components", []))
    print(f"  核心组件: {len(components.split(chr(10)))} 行")
    
    # 渲染使用示例
    examples = engine._render_usage_examples(analysis_data.get("usage_examples", []))
    print(f"  使用示例: {len(examples.split(chr(10)))} 行")
    
    # 完整渲染
    print("\n执行完整模板渲染...")
    content = engine.render(analysis_data)
    
    return content


def demonstrate_final_output(content: str, module_path: Path):
    """演示最终输出"""
    print("\n" + "=" * 60)
    print("3. 最终输出")
    print("=" * 60)
    
    print(f"生成的文档长度: {len(content)} 字符")
    print(f"生成的文档行数: {len(content.split(chr(10)))} 行")
    
    # 保存到文件
    doc_path = module_path / ".ac.mod.md"
    doc_path.write_text(content, encoding='utf-8')
    print(f"文档已保存到: {doc_path}")
    
    # 显示文档预览
    print("\n文档预览 (前20行):")
    print("-" * 40)
    lines = content.split('\n')
    for i, line in enumerate(lines[:20], 1):
        print(f"{i:2d}: {line}")
    if len(lines) > 20:
        print(f"... (还有 {len(lines) - 20} 行)")
    
    return doc_path


def main():
    """主演示函数"""
    print("模板生成完整流程演示")
    print("=" * 60)
    
    # 创建演示模块
    print("创建演示模块...")
    demo_module = create_demo_module()
    print(f"演示模块创建完成: {demo_module}")
    
    try:
        # 1. 数据收集
        analysis_data = demonstrate_data_collection(demo_module)
        
        # 2. 模板渲染
        content = demonstrate_template_rendering(analysis_data)
        
        # 3. 最终输出
        doc_path = demonstrate_final_output(content, demo_module)
        
        print("\n" + "=" * 60)
        print("演示完成！")
        print("=" * 60)
        print(f"生成的文档位于: {doc_path}")
        print("\n模板生成的关键步骤:")
        print("1. 模块扫描 - 分析代码结构和组件")
        print("2. 数据提取 - 收集类、函数、文档字符串等")
        print("3. 模板加载 - 使用默认或自定义模板")
        print("4. 变量替换 - 将分析数据填入模板")
        print("5. 内容渲染 - 生成最终的Markdown文档")
        print("6. 文件输出 - 保存为.ac.mod.md文件")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
