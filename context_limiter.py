"""
Context Limiter for AC Module Generator

This module provides functionality to prevent context overflow when generating
.ac.mod.md files for large Java modules with many files.
"""

import os
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

try:
    # Try to import from the main project
    from src.autocoder.rag.token_counter import count_tokens
    from src.autocoder.common.pruner.context_pruner import PruneContext
    from src.autocoder.common import SourceCode, AutoCoderArgs
    import byzerllm
except ImportError:
    # Fallback for standalone usage
    try:
        from autocoder.rag.token_counter import count_tokens
        from autocoder.common.pruner.context_pruner import PruneContext
        from autocoder.common import SourceCode, AutoCoderArgs
        import byzerllm
    except ImportError:
        # Mock implementations for testing
        def count_tokens(text: str) -> int:
            """Mock token counter - estimates 4 chars per token"""
            return len(text) // 4

        class SourceCode:
            def __init__(self, module_name: str, source_code: str, tokens: int = 0):
                self.module_name = module_name
                self.source_code = source_code
                self.tokens = tokens

        class AutoCoderArgs:
            def __init__(self):
                self.context_prune_safe_zone_tokens = 1000
                self.context_prune_strategy = "extract"
                self.context_prune_sliding_window_size = 100
                self.context_prune_sliding_window_overlap = 20

        class PruneContext:
            def __init__(self, max_tokens: int, args: AutoCoderArgs, llm=None, verbose: bool = False):
                self.max_tokens = max_tokens
                self.args = args
                self.verbose = verbose

            def handle_overflow(self, file_sources: List[SourceCode], conversations: List[Dict[str, str]], strategy: str = "extract") -> List[SourceCode]:
                # Mock implementation - just return first few files
                total_tokens = sum(f.tokens for f in file_sources)
                if total_tokens <= self.max_tokens:
                    return file_sources

                # Simple truncation
                selected = []
                current_tokens = 0
                for file_source in file_sources:
                    if current_tokens + file_source.tokens <= self.max_tokens:
                        selected.append(file_source)
                        current_tokens += file_source.tokens
                    else:
                        break
                return selected

        # Mock byzerllm for standalone usage
        class MockByzerLLM:
            def __init__(self):
                pass

        byzerllm = type('MockModule', (), {'ByzerLLM': MockByzerLLM})()

try:
    from .file_utils import FileUtils
except ImportError:
    from file_utils import FileUtils

logger = logging.getLogger(__name__)


@dataclass
class ModuleAnalysisResult:
    """Result of module analysis for context limiting"""
    total_files: int
    total_tokens: int
    java_files: int
    java_tokens: int
    python_files: int
    python_tokens: int
    other_files: int
    other_tokens: int
    exceeds_limit: bool
    recommended_strategy: str
    file_breakdown: List[Dict[str, Any]]


@dataclass
class ContextLimitConfig:
    """Configuration for context limiting"""
    max_tokens: int = 32000  # Default context window limit
    safe_zone_tokens: int = 8000  # Reserve tokens for generation
    java_file_priority: bool = True  # Prioritize Java files over others
    include_test_files: bool = False  # Whether to include test files
    max_files_per_type: int = 50  # Maximum files per file type
    sliding_window_size: int = 100  # Lines per sliding window
    sliding_window_overlap: int = 20  # Overlap between windows


class ContextLimiter:
    """
    Context limiter for AC module generation.
    
    Prevents context overflow when analyzing large Java modules by:
    1. Analyzing module size and complexity
    2. Prioritizing important files
    3. Using intelligent content extraction
    4. Applying sliding window for large files
    """
    
    def __init__(self, config: Optional[ContextLimitConfig] = None):
        """
        Initialize the context limiter.
        
        Args:
            config: Configuration for context limiting
        """
        self.config = config or ContextLimitConfig()
        self.file_utils = FileUtils()
        
        # Initialize mock LLM and args for PruneContext
        try:
            # Try to create a real LLM instance
            self.llm = byzerllm.ByzerLLM()
        except:
            # Use None for mock
            self.llm = None
        
        self.args = AutoCoderArgs()
        self.args.context_prune_safe_zone_tokens = self.config.safe_zone_tokens
        self.args.context_prune_strategy = "extract"
        self.args.context_prune_sliding_window_size = self.config.sliding_window_size
        self.args.context_prune_sliding_window_overlap = self.config.sliding_window_overlap
        
        # Initialize pruner
        self.pruner = PruneContext(
            max_tokens=self.config.max_tokens - self.config.safe_zone_tokens,
            args=self.args,
            llm=self.llm,
            verbose=True
        )
    
    def analyze_module(self, module_path: Path) -> ModuleAnalysisResult:
        """
        Analyze a module to determine if it exceeds context limits.
        
        Args:
            module_path: Path to the module directory
            
        Returns:
            ModuleAnalysisResult with analysis details
        """
        logger.info(f"Analyzing module for context limits: {module_path}")
        
        # Find all relevant files
        java_files = self._find_java_files(module_path)
        python_files = self._find_python_files(module_path)
        other_files = self._find_other_files(module_path)
        
        # Count tokens for each file type
        java_tokens, java_breakdown = self._count_tokens_for_files(java_files, "java")
        python_tokens, python_breakdown = self._count_tokens_for_files(python_files, "python")
        other_tokens, other_breakdown = self._count_tokens_for_files(other_files, "other")
        
        total_tokens = java_tokens + python_tokens + other_tokens
        total_files = len(java_files) + len(python_files) + len(other_files)
        
        # Determine if limits are exceeded
        exceeds_limit = total_tokens > self.config.max_tokens
        
        # Recommend strategy
        recommended_strategy = self._recommend_strategy(
            total_tokens, total_files, java_tokens, python_tokens
        )
        
        # Combine all file breakdowns
        file_breakdown = java_breakdown + python_breakdown + other_breakdown
        
        return ModuleAnalysisResult(
            total_files=total_files,
            total_tokens=total_tokens,
            java_files=len(java_files),
            java_tokens=java_tokens,
            python_files=len(python_files),
            python_tokens=python_tokens,
            other_files=len(other_files),
            other_tokens=other_tokens,
            exceeds_limit=exceeds_limit,
            recommended_strategy=recommended_strategy,
            file_breakdown=file_breakdown
        )

    def limit_module_content(self, module_path: Path, conversations: List[Dict[str, str]] = None) -> Tuple[List[SourceCode], ModuleAnalysisResult]:
        """
        Apply context limiting to module content.

        Args:
            module_path: Path to the module directory
            conversations: Optional conversation context for intelligent extraction

        Returns:
            Tuple of (limited_source_codes, analysis_result)
        """
        # First analyze the module
        analysis = self.analyze_module(module_path)

        if not analysis.exceeds_limit:
            # No limiting needed, return all files
            source_codes = self._create_source_codes_from_breakdown(analysis.file_breakdown)
            return source_codes, analysis

        logger.warning(f"Module {module_path} exceeds context limit ({analysis.total_tokens} > {self.config.max_tokens})")
        logger.info(f"Applying {analysis.recommended_strategy} strategy")

        # Create source codes for all files
        all_source_codes = self._create_source_codes_from_breakdown(analysis.file_breakdown)

        # Apply limiting strategy
        if conversations is None:
            conversations = [{"role": "user", "content": f"Generate documentation for Java module at {module_path}"}]

        limited_source_codes = self.pruner.handle_overflow(
            file_sources=all_source_codes,
            conversations=conversations,
            strategy=analysis.recommended_strategy
        )

        logger.info(f"Context limiting reduced files from {len(all_source_codes)} to {len(limited_source_codes)}")

        return limited_source_codes, analysis

    def _find_java_files(self, module_path: Path) -> List[Path]:
        """Find Java files in the module."""
        java_files = self.file_utils.find_files_by_extension(module_path, ['.java'])

        if not self.config.include_test_files:
            # Filter out test files
            java_files = [f for f in java_files if not self._is_test_file(f)]

        # Limit number of files
        if len(java_files) > self.config.max_files_per_type:
            logger.warning(f"Too many Java files ({len(java_files)}), limiting to {self.config.max_files_per_type}")
            java_files = self._prioritize_java_files(java_files)[:self.config.max_files_per_type]

        return java_files

    def _find_python_files(self, module_path: Path) -> List[Path]:
        """Find Python files in the module."""
        python_files = self.file_utils.find_files_by_extension(module_path, ['.py'])

        if not self.config.include_test_files:
            # Filter out test files
            python_files = [f for f in python_files if not self._is_test_file(f)]

        # Limit number of files
        if len(python_files) > self.config.max_files_per_type:
            logger.warning(f"Too many Python files ({len(python_files)}), limiting to {self.config.max_files_per_type}")
            python_files = python_files[:self.config.max_files_per_type]

        return python_files

    def _find_other_files(self, module_path: Path) -> List[Path]:
        """Find other relevant files (config, docs, etc.)."""
        other_extensions = ['.xml', '.gradle', '.properties', '.yml', '.yaml', '.json', '.md', '.txt']
        other_files = []

        for ext in other_extensions:
            files = self.file_utils.find_files_by_extension(module_path, [ext])
            other_files.extend(files)

        # Prioritize important files
        important_files = []
        regular_files = []

        for file_path in other_files:
            if self._is_important_config_file(file_path):
                important_files.append(file_path)
            else:
                regular_files.append(file_path)

        # Combine with important files first
        combined_files = important_files + regular_files

        # Limit total number
        max_other_files = min(20, self.config.max_files_per_type // 2)
        if len(combined_files) > max_other_files:
            combined_files = combined_files[:max_other_files]

        return combined_files

    def _count_tokens_for_files(self, files: List[Path], file_type: str) -> Tuple[int, List[Dict[str, Any]]]:
        """Count tokens for a list of files and return breakdown."""
        total_tokens = 0
        breakdown = []

        for file_path in files:
            try:
                content = self.file_utils.read_file(file_path)
                tokens = count_tokens(content)
                total_tokens += tokens

                breakdown.append({
                    "path": str(file_path),
                    "type": file_type,
                    "tokens": tokens,
                    "size_bytes": len(content.encode('utf-8')),
                    "lines": content.count('\n') + 1,
                    "content": content
                })

            except Exception as e:
                logger.warning(f"Failed to read file {file_path}: {e}")
                breakdown.append({
                    "path": str(file_path),
                    "type": file_type,
                    "tokens": 0,
                    "size_bytes": 0,
                    "lines": 0,
                    "content": "",
                    "error": str(e)
                })

        return total_tokens, breakdown

    def _recommend_strategy(self, total_tokens: int, total_files: int, java_tokens: int, python_tokens: int) -> str:
        """Recommend the best strategy based on module characteristics."""
        if total_tokens <= self.config.max_tokens:
            return "none"

        # If we have a moderate number of files but high token count, use extract
        if total_files <= 30 and total_tokens > self.config.max_tokens * 1.5:
            return "extract"

        # If we have many files, use score to filter
        if total_files > 50:
            return "score"

        # Default to extract for intelligent content selection
        return "extract"

    def _create_source_codes_from_breakdown(self, file_breakdown: List[Dict[str, Any]]) -> List[SourceCode]:
        """Create SourceCode objects from file breakdown."""
        source_codes = []

        for file_info in file_breakdown:
            if "error" not in file_info and file_info["content"]:
                source_code = SourceCode(
                    module_name=file_info["path"],
                    source_code=file_info["content"],
                    tokens=file_info["tokens"]
                )
                source_codes.append(source_code)

        return source_codes

    def _is_test_file(self, file_path: Path) -> bool:
        """Check if a file is a test file."""
        file_str = str(file_path).lower()

        # Java test patterns
        if file_path.suffix == '.java':
            return (
                file_path.name.endswith('Test.java') or
                file_path.name.endswith('Tests.java') or
                'test' in file_str
            )

        # Python test patterns
        if file_path.suffix == '.py':
            return (
                file_path.name.startswith('test_') or
                file_path.name.endswith('_test.py') or
                '/tests/' in file_str
            )

        return False

    def _prioritize_java_files(self, java_files: List[Path]) -> List[Path]:
        """Prioritize Java files by importance."""
        # Categorize files
        main_classes = []
        interfaces = []
        services = []
        controllers = []
        models = []
        utils = []
        others = []

        for file_path in java_files:
            name_lower = file_path.name.lower()

            if 'main' in name_lower or 'application' in name_lower:
                main_classes.append(file_path)
            elif name_lower.endswith('interface.java') or self._contains_interface_keyword(file_path):
                interfaces.append(file_path)
            elif 'service' in name_lower:
                services.append(file_path)
            elif 'controller' in name_lower:
                controllers.append(file_path)
            elif 'model' in name_lower or 'entity' in name_lower or 'dto' in name_lower:
                models.append(file_path)
            elif 'util' in name_lower or 'helper' in name_lower:
                utils.append(file_path)
            else:
                others.append(file_path)

        # Return in priority order
        return main_classes + interfaces + services + controllers + models + utils + others

    def _contains_interface_keyword(self, file_path: Path) -> bool:
        """Check if a Java file contains interface declaration."""
        try:
            content = self.file_utils.read_file(file_path)
            # Simple check for interface keyword
            return 'interface ' in content
        except:
            return False

    def _is_important_config_file(self, file_path: Path) -> bool:
        """Check if a file is an important configuration file."""
        important_files = {
            'pom.xml', 'build.gradle', 'build.gradle.kts',
            'application.properties', 'application.yml', 'application.yaml',
            'readme.md', 'readme.txt', 'changelog.md',
            'dockerfile', 'docker-compose.yml'
        }

        return file_path.name.lower() in important_files

    def get_context_summary(self, analysis: ModuleAnalysisResult) -> str:
        """Get a human-readable summary of the context analysis."""
        summary = []
        summary.append(f"Module Analysis Summary:")
        summary.append(f"  Total files: {analysis.total_files}")
        summary.append(f"  Total tokens: {analysis.total_tokens:,}")
        summary.append(f"  Java files: {analysis.java_files} ({analysis.java_tokens:,} tokens)")
        summary.append(f"  Python files: {analysis.python_files} ({analysis.python_tokens:,} tokens)")
        summary.append(f"  Other files: {analysis.other_files} ({analysis.other_tokens:,} tokens)")
        summary.append(f"  Exceeds limit: {'Yes' if analysis.exceeds_limit else 'No'}")
        summary.append(f"  Recommended strategy: {analysis.recommended_strategy}")

        if analysis.exceeds_limit:
            percentage_over = ((analysis.total_tokens - self.config.max_tokens) / self.config.max_tokens) * 100
            summary.append(f"  Over limit by: {percentage_over:.1f}%")

        return "\n".join(summary)
