# AC Module Generator - LLM Configuration Example
# Copy this file to .env and fill in your actual values

# OpenAI Configuration
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.1

# Alternative: Azure OpenAI Configuration
# OPENAI_API_BASE=https://your-resource.openai.azure.com/openai/deployments/your-deployment/chat/completions?api-version=2023-05-15
# OPENAI_API_KEY=your_azure_api_key
# OPENAI_MODEL=gpt-35-turbo

# Alternative: Local LLM Server (e.g., Ollama, vLLM)
# OPENAI_API_BASE=http://localhost:8000/v1
# OPENAI_API_KEY=not_required_for_local
# OPENAI_MODEL=llama2

# Alternative: Other OpenAI-compatible APIs
# OPENAI_API_BASE=https://api.anthropic.com/v1
# OPENAI_API_KEY=your_anthropic_api_key
# OPENAI_MODEL=claude-3-sonnet
