#!/usr/bin/env python3
"""
Test the intelligent fallback mechanism of AC Module Generator

This script demonstrates various scenarios where the system falls back
from LLM generation to template generation.
"""

import tempfile
import unittest
from pathlib import Path
from unittest.mock import patch, Mock

from generator import ACModGenerator
from llm_config import LLMConfig, LLMClient


class TestFallbackMechanism(unittest.TestCase):
    """Test various fallback scenarios"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_module_path = Path(self.temp_dir) / "test_module"
        self.test_module_path.mkdir()
        
        # Create a simple Python file
        (self.test_module_path / "__init__.py").write_text(
            '"""Test module"""\n'
        )
        (self.test_module_path / "main.py").write_text(
            'def hello():\n    return "Hello"\n'
        )
    
    def test_scenario_1_no_api_key(self):
        """场景1: 没有配置API密钥 - 直接使用模板"""
        print("\n=== 场景1: 没有API密钥 ===")
        
        # 不配置API密钥
        generator = ACModGenerator(root_path=self.temp_dir)
        
        # 生成文档
        success = generator.generate_documentation(self.test_module_path, force=True)
        self.assertTrue(success)
        
        # 检查生成的文档
        doc_path = self.test_module_path / ".ac.mod.md"
        content = doc_path.read_text()
        
        # 应该包含模板生成的内容
        self.assertIn("test_module", content)
        self.assertIn("目录结构", content)
        print("✓ 成功使用模板生成文档")
    
    def test_scenario_2_invalid_api_key(self):
        """场景2: API密钥无效 - LLM调用失败，回退到模板"""
        print("\n=== 场景2: API密钥无效 ===")
        
        # 配置无效的API密钥
        llm_config = LLMConfig(
            api_base="https://api.openai.com/v1",
            api_key="invalid_key",
            model="gpt-3.5-turbo"
        )
        generator = ACModGenerator(root_path=self.temp_dir, llm_config=llm_config)
        
        # Mock OpenAI客户端抛出认证错误
        with patch('openai.OpenAI') as mock_openai:
            mock_client = Mock()
            mock_client.chat.completions.create.side_effect = Exception("Invalid API key")
            mock_openai.return_value = mock_client
            
            # 生成文档
            success = generator.generate_documentation(self.test_module_path, force=True)
            self.assertTrue(success)
            
            # 检查生成的文档
            doc_path = self.test_module_path / ".ac.mod.md"
            content = doc_path.read_text()
            
            # 应该包含模板生成的内容（因为LLM失败了）
            self.assertIn("test_module", content)
            self.assertIn("目录结构", content)
            print("✓ LLM调用失败，成功回退到模板生成")
    
    def test_scenario_3_network_error(self):
        """场景3: 网络错误 - LLM调用失败，回退到模板"""
        print("\n=== 场景3: 网络错误 ===")
        
        llm_config = LLMConfig(
            api_base="https://api.openai.com/v1",
            api_key="valid_key",
            model="gpt-3.5-turbo"
        )
        generator = ACModGenerator(root_path=self.temp_dir, llm_config=llm_config)
        
        # Mock网络错误
        with patch('openai.OpenAI') as mock_openai:
            mock_client = Mock()
            mock_client.chat.completions.create.side_effect = Exception("Network timeout")
            mock_openai.return_value = mock_client
            
            success = generator.generate_documentation(self.test_module_path, force=True)
            self.assertTrue(success)
            
            doc_path = self.test_module_path / ".ac.mod.md"
            content = doc_path.read_text()
            
            self.assertIn("test_module", content)
            print("✓ 网络错误，成功回退到模板生成")
    
    def test_scenario_4_empty_llm_response(self):
        """场景4: LLM返回空内容 - 回退到模板"""
        print("\n=== 场景4: LLM返回空内容 ===")
        
        llm_config = LLMConfig(api_key="valid_key")
        generator = ACModGenerator(root_path=self.temp_dir, llm_config=llm_config)
        
        # Mock LLM返回空内容
        with patch('openai.OpenAI') as mock_openai:
            mock_response = Mock()
            mock_response.choices = []  # 空的choices
            
            mock_client = Mock()
            mock_client.chat.completions.create.return_value = mock_response
            mock_openai.return_value = mock_client
            
            success = generator.generate_documentation(self.test_module_path, force=True)
            self.assertTrue(success)
            
            doc_path = self.test_module_path / ".ac.mod.md"
            content = doc_path.read_text()
            
            self.assertIn("test_module", content)
            print("✓ LLM返回空内容，成功回退到模板生成")
    
    def test_scenario_5_successful_llm(self):
        """场景5: LLM成功生成 - 使用LLM内容"""
        print("\n=== 场景5: LLM成功生成 ===")
        
        llm_config = LLMConfig(api_key="valid_key")
        generator = ACModGenerator(root_path=self.temp_dir, llm_config=llm_config)
        
        # Mock成功的LLM响应
        with patch('openai.OpenAI') as mock_openai:
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = "# AI生成的文档\n\n这是由AI生成的模块文档。"
            
            mock_client = Mock()
            mock_client.chat.completions.create.return_value = mock_response
            mock_openai.return_value = mock_client
            
            success = generator.generate_documentation(self.test_module_path, force=True)
            self.assertTrue(success)
            
            doc_path = self.test_module_path / ".ac.mod.md"
            content = doc_path.read_text()
            
            # 应该包含LLM生成的内容
            self.assertIn("AI生成的文档", content)
            self.assertIn("由AI生成的模块文档", content)
            print("✓ LLM成功生成文档")
    
    def test_scenario_6_partial_llm_failure(self):
        """场景6: LLM部分失败 - 先尝试LLM，失败后回退"""
        print("\n=== 场景6: LLM部分失败 ===")
        
        llm_config = LLMConfig(api_key="valid_key")
        generator = ACModGenerator(root_path=self.temp_dir, llm_config=llm_config)
        
        # 直接测试_generate_content方法
        analysis = {
            "name": "test_module",
            "path": "./test_module",
            "description": "Test module",
            "directory_structure": {},
            "core_components": [],
            "dependencies": [],
            "usage_examples": [],
            "test_commands": []
        }
        
        # Mock LLM客户端返回None（失败）
        with patch.object(generator.llm_client, 'generate_documentation', return_value=None):
            content = generator._generate_content(analysis)
            
            # 应该包含模板生成的内容
            self.assertIn("test_module", content)
            self.assertIn("目录结构", content)
            print("✓ LLM部分失败，成功回退到模板")


def demonstrate_fallback_scenarios():
    """演示回退机制的各种场景"""
    print("智能回退机制演示")
    print("=" * 50)
    
    print("\n回退机制的触发条件：")
    print("1. 没有配置API密钥")
    print("2. API密钥无效或过期")
    print("3. 网络连接问题")
    print("4. API服务不可用")
    print("5. LLM返回空内容或格式错误")
    print("6. 超过API调用限制")
    print("7. 其他任何LLM调用异常")
    
    print("\n在所有这些情况下，系统都会：")
    print("✓ 记录错误信息到日志")
    print("✓ 自动切换到模板生成")
    print("✓ 确保文档生成功能正常工作")
    print("✓ 用户无需手动干预")


if __name__ == "__main__":
    demonstrate_fallback_scenarios()
    print("\n" + "=" * 50)
    print("运行测试用例...")
    unittest.main(verbosity=2)
