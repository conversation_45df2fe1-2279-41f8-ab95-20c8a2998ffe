# AC Module Documentation Generator

一个独立的功能组件，用于自动生成和维护 Auto-Coder 项目中的 `.ac.mod.md` 模块文档文件。

## 特性

- 🔍 **智能模块发现**: 自动扫描项目目录，识别潜在的 AC 模块
- 📊 **深度代码分析**: 使用 AST 解析 Python 和 Java 代码，提取类、函数和文档字符串
- 🏗️ **分层文档生成**: 为大型Java模块的子包生成独立文档，提供更好的粒度和可维护性
- 🔗 **依赖关系分析**: 自动发现模块间的依赖关系
- 📝 **标准化文档**: 生成符合 AC 模块规范的标准化文档
- 🔄 **增量更新**: 支持更新现有文档而不丢失手动修改
- 🎨 **自定义模板**: 支持用户自定义文档模板
- 🖥️ **命令行工具**: 提供友好的命令行接口
- ⚡ **上下文限制**: 智能处理大型 Java 模块，防止上下文窗口超限
- 🎯 **智能裁剪**: 基于相关性的文件过滤和代码片段提取
- 🤖 **AI 驱动**: 支持 OpenAI-like API 的智能文档生成
- ✅ **完整测试**: 包含全面的单元测试和集成测试

## 快速开始

### 安装

该组件是 Auto-Coder 项目的一部分，无需单独安装。

### 基本使用

#### 1. 编程接口

```python
from autocoder.ac_mod_generator import ACModGenerator
from pathlib import Path

# 初始化生成器
generator = ACModGenerator(root_path="./src")

# 发现所有潜在模块
modules = generator.discover_modules()
print(f"发现 {len(modules)} 个潜在模块")

# 为单个模块生成文档
module_path = Path("./src/my_module")
success = generator.generate_documentation(module_path)
if success:
    print(f"✓ 成功生成文档: {module_path}")

# 为大型Java模块生成分层文档（推荐）
java_module_path = Path("./src/large_java_module")
hierarchical_results = generator.generate_hierarchical_documentation(java_module_path)
print(f"生成了 {len(hierarchical_results)} 个文档文件")

# 批量生成所有模块文档
results = generator.generate_all_documentation(force=False)
success_count = sum(1 for success in results.values() if success)
print(f"成功生成 {success_count}/{len(results)} 个模块的文档")

# 批量生成所有模块的分层文档
all_hierarchical = generator.generate_all_hierarchical_documentation(force=False)
total_docs = sum(len(results) for results in all_hierarchical.values())
print(f"分层文档生成完成，共生成 {total_docs} 个文档文件")
```

#### 2. 命令行接口

```bash
# 查看帮助
python -m autocoder.ac_mod_generator.cli --help

# 列出所有潜在模块
python -m autocoder.ac_mod_generator.cli list

# 为特定模块生成文档
python -m autocoder.ac_mod_generator.cli generate ./src/my_module

# 为大型Java模块生成分层文档（推荐）
python -m autocoder.ac_mod_generator.cli generate ./src/large_java_module --hierarchical

# 批量生成所有模块文档
python -m autocoder.ac_mod_generator.cli generate-all --root-path ./src

# 批量生成所有模块的分层文档
python -m autocoder.ac_mod_generator.cli generate-all --hierarchical --root-path ./src

# 更新现有文档
python -m autocoder.ac_mod_generator.cli update ./src/my_module

# 使用自定义模板
python -m autocoder.ac_mod_generator.cli generate ./src/my_module --template ./custom_template.md

# 强制覆盖现有文档
python -m autocoder.ac_mod_generator.cli generate-all --force --yes

# 检查模块是否超过上下文限制
python -m autocoder.ac_mod_generator.cli check-context ./src/large_java_module --show-files

# 为大型 Java 模块生成文档（启用上下文限制）
python -m autocoder.ac_mod_generator.cli generate ./src/large_java_module --enable-context-limiting --max-tokens 24000

# 包含测试文件的分析
python -m autocoder.ac_mod_generator.cli generate ./src/my_module --enable-context-limiting --include-tests

# 使用 LLM 生成文档（AI 驱动）
python -m autocoder.ac_mod_generator.cli generate ./src/my_module --api-base https://api.openai.com/v1 --api-key your_api_key --model gpt-3.5-turbo
```

## 上下文限制功能

### 概述

对于包含大量文件的 Java 模块，生成文档时可能会超过大语言模型的上下文窗口限制。本组件提供了智能的上下文限制功能来解决这个问题。

### 主要特性

- **智能文件优先级**: 自动识别重要文件（主类、接口、服务类等）
- **代码片段提取**: 从大文件中提取相关代码片段而非完整内容
- **滑动窗口处理**: 对超大文件使用滑动窗口分析
- **多种裁剪策略**: 支持评分过滤、片段提取、简单删除三种策略

### 使用方法

#### 1. 检查模块上下文限制

```python
from autocoder.ac_mod_generator import ACModGenerator
from pathlib import Path

generator = ACModGenerator()
result = generator.check_module_context_limits(
    Path("./src/large_java_module"),
    max_tokens=32000
)

print(f"超过限制: {result['exceeds_limit']}")
print(f"总文件数: {result['total_files']}")
print(f"总 Token 数: {result['total_tokens']:,}")
print(f"推荐策略: {result['recommended_strategy']}")
```

#### 2. 生成带上下文限制的文档

```python
from autocoder.ac_mod_generator import ACModGenerator
from autocoder.ac_mod_generator.context_limiter import ContextLimitConfig

# 配置上下文限制
config = ContextLimitConfig(
    max_tokens=24000,           # 最大 Token 限制
    safe_zone_tokens=6000,      # 预留生成空间
    java_file_priority=True,    # Java 文件优先
    include_test_files=False,   # 排除测试文件
    max_files_per_type=30       # 每种类型最大文件数
)

generator = ACModGenerator(context_config=config)

# 生成文档
success = generator.generate_documentation_with_context_limiting(
    Path("./src/large_java_module"),
    force=True,
    max_tokens=24000
)
```

#### 3. 命令行使用

```bash
# 检查上下文限制
python -m autocoder.ac_mod_generator.cli check-context ./src/my_java_module --max-tokens 20000 --show-files

# 生成文档（启用上下文限制）
python -m autocoder.ac_mod_generator.cli generate ./src/my_java_module --enable-context-limiting --max-tokens 20000
```

### 配置选项

| 参数                     | 类型 | 默认值 | 说明                   |
| ------------------------ | ---- | ------ | ---------------------- |
| `max_tokens`             | int  | 32000  | 最大 Token 限制        |
| `safe_zone_tokens`       | int  | 8000   | 预留给生成的 Token 数  |
| `java_file_priority`     | bool | True   | 是否优先处理 Java 文件 |
| `include_test_files`     | bool | False  | 是否包含测试文件       |
| `max_files_per_type`     | int  | 50     | 每种文件类型的最大数量 |
| `sliding_window_size`    | int  | 100    | 滑动窗口大小（行数）   |
| `sliding_window_overlap` | int  | 20     | 滑动窗口重叠（行数）   |

## LLM 集成功能

### 概述

AC 模块生成器现在支持使用大语言模型（LLM）来生成更智能、更详细的文档内容。支持 OpenAI API 以及任何兼容 OpenAI API 格式的服务。

### 支持的 LLM 提供商

- **OpenAI**: GPT-3.5, GPT-4 等模型
- **Azure OpenAI**: 企业级 OpenAI 服务
- **本地 LLM 服务器**: Ollama, vLLM, FastChat 等
- **其他兼容服务**: 任何实现 OpenAI API 格式的服务

### 配置方式

#### 1. 命令行参数

```bash
# 使用 OpenAI API
python -m autocoder.ac_mod_generator.cli generate ./src/my_module \
    --api-base https://api.openai.com/v1 \
    --api-key your_openai_api_key \
    --model gpt-3.5-turbo

# 使用本地 LLM 服务器
python -m autocoder.ac_mod_generator.cli generate ./src/my_module \
    --api-base http://localhost:8000/v1 \
    --api-key not_required \
    --model llama2
```

#### 2. 环境变量

```bash
# 设置环境变量
export OPENAI_API_BASE="https://api.openai.com/v1"
export OPENAI_API_KEY="your_api_key_here"
export OPENAI_MODEL="gpt-3.5-turbo"
export OPENAI_MAX_TOKENS="4000"
export OPENAI_TEMPERATURE="0.1"

# 然后直接运行（会自动读取环境变量）
python -m autocoder.ac_mod_generator.cli generate ./src/my_module
```

#### 3. 编程接口

```python
from autocoder.ac_mod_generator import ACModGenerator
from autocoder.ac_mod_generator.llm_config import LLMConfig

# 方式1：初始化时配置
llm_config = LLMConfig(
    api_base="https://api.openai.com/v1",
    api_key="your_api_key_here",
    model="gpt-3.5-turbo",
    max_tokens=4000,
    temperature=0.1
)

generator = ACModGenerator(
    root_path="./src",
    llm_config=llm_config
)

# 方式2：运行时配置
generator = ACModGenerator(root_path="./src")
generator.set_llm_config(
    api_base="https://api.openai.com/v1",
    api_key="your_api_key_here",
    model="gpt-3.5-turbo"
)

# 生成文档（会自动使用 LLM 如果配置可用）
success = generator.generate_documentation(Path("./src/my_module"))
```

### LLM 配置参数

| 参数          | 类型  | 默认值                    | 说明                          |
| ------------- | ----- | ------------------------- | ----------------------------- |
| `api_base`    | str   | https://api.openai.com/v1 | API 端点 URL                  |
| `api_key`     | str   | None                      | API 密钥                      |
| `model`       | str   | gpt-3.5-turbo             | 模型名称                      |
| `max_tokens`  | int   | 4000                      | 最大生成 token 数             |
| `temperature` | float | 0.1                       | 生成温度（0-1，越低越确定性） |
| `timeout`     | int   | 60                        | 请求超时时间（秒）            |

### 智能回退机制

当 LLM 不可用时（如未配置 API 密钥、网络错误等），系统会自动回退到基于模板的文档生成，确保功能的可靠性。

### 常见配置示例

#### OpenAI 官方 API
```bash
export OPENAI_API_BASE="https://api.openai.com/v1"
export OPENAI_API_KEY="sk-..."
export OPENAI_MODEL="gpt-3.5-turbo"
```

#### Azure OpenAI
```bash
export OPENAI_API_BASE="https://your-resource.openai.azure.com/openai/deployments/your-deployment/chat/completions?api-version=2023-05-15"
export OPENAI_API_KEY="your_azure_key"
export OPENAI_MODEL="gpt-35-turbo"
```

#### 本地 Ollama 服务器
```bash
export OPENAI_API_BASE="http://localhost:11434/v1"
export OPENAI_API_KEY="not_required"
export OPENAI_MODEL="llama2"
```

### 裁剪策略

1. **score**: 基于 LLM 评分的智能过滤（推荐）
2. **extract**: 提取相关代码片段
3. **delete**: 简单删除超出部分

## 高级用法

### 自定义模板

创建自定义模板文件 `custom_template.md`:

```markdown
# {{MODULE_NAME}}

{{MODULE_DESCRIPTION}}

## 模块结构

{{DIRECTORY_STRUCTURE}}

## 核心功能

{{CORE_COMPONENTS}}

## 使用示例

{{USAGE_EXAMPLES}}

## 依赖模块

{{DEPENDENCIES}}

## 测试方法

{{TEST_COMMANDS}}
```

然后使用自定义模板：

```python
generator = ACModGenerator(root_path="./src", template_path="./custom_template.md")
```

### 集成到现有工具

该组件可以轻松集成到现有的 Auto-Coder 工具链中：

```python
# 与现有的 ACModWriteTool 集成
from autocoder.ac_mod_generator import ACModGenerator

def enhanced_ac_mod_write(module_path: str):
    generator = ACModGenerator()
    
    # 先生成基础文档
    generator.generate_documentation(Path(module_path))
    
    # 然后可以进行进一步的自定义修改
    # ...
```

## 配置选项

| 参数            | 类型     | 默认值 | 说明               |
| --------------- | -------- | ------ | ------------------ |
| `root_path`     | str/Path | "."    | 项目根目录路径     |
| `template_path` | str/None | None   | 自定义模板文件路径 |
| `force`         | bool     | False  | 是否覆盖现有文档   |
| `recursive`     | bool     | True   | 是否递归扫描子目录 |

## 文档结构

生成的 `.ac.mod.md` 文件包含以下标准化章节：

1. **模块标题和描述**: 模块名称和一句话功能描述
2. **目录结构**: 可视化的目录树，包含文件功能说明
3. **快速开始**: 基本使用方式、辅助函数说明、配置管理
4. **核心组件详解**: 主要类和函数的详细说明
5. **依赖关系说明**: 对其他 AC 模块的依赖列表
6. **测试命令**: 可执行的验证命令

## 开发和测试

### 运行测试

```bash
# 运行所有测试
pytest src/autocoder/ac_mod_generator/tests/ -v

# 运行特定测试
pytest src/autocoder/ac_mod_generator/tests/test_generator.py -v

# 生成测试覆盖率报告
pytest src/autocoder/ac_mod_generator/tests/ --cov=src/autocoder/ac_mod_generator --cov-report=html
```

### 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 许可证

该组件遵循 Auto-Coder 项目的许可证。

## 更新日志

### v1.1.0
- ⚡ **新增**: Java 模块上下文限制功能
- 🎯 **新增**: 智能文件优先级和代码片段提取
- 📊 **新增**: 上下文分析和检查命令
- 🔧 **改进**: 支持 Java 代码分析和文档生成
- 🖥️ **改进**: 命令行工具增加上下文限制选项
- 📝 **改进**: 文档生成包含上下文限制信息

### v1.0.0
- 初始版本发布
- 支持基本的模块发现和文档生成
- 提供命令行接口
- 包含完整的测试套件
