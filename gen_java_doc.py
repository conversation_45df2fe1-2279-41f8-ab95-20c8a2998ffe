#!/usr/bin/env python3
"""
简洁的 Java 项目文档生成脚本

使用方法:
  python gen_java_doc.py <java_project_path>
  python gen_java_doc.py <java_project_path> --llm --api-key your_key
"""

import sys
import os
from pathlib import Path
from generator import ACModGenerator
from llm_config import LLMConfig


def find_java_modules(root_path: Path):
    """查找所有 Java 模块"""
    modules = []

    # 检查根目录
    if list(root_path.rglob("*.java")):
        modules.append(root_path)

    # 查找子模块
    for item in root_path.iterdir():
        if item.is_dir() and not item.name.startswith('.'):
            java_files = list(item.rglob("*.java"))
            if java_files and ((item / "pom.xml").exists() or
                              (item / "build.gradle").exists() or
                              (item / "src").exists()):
                modules.append(item)

    return modules


def generate_doc(project_path_str: str, use_llm: bool = False, api_key: str = None) -> bool:
    """生成 Java 项目文档"""
    project_path = Path(project_path_str).resolve()

    # 基本验证
    if not project_path.exists():
        print(f"❌ 路径不存在: {project_path}")
        return False

    if not project_path.is_dir():
        print(f"❌ 不是目录: {project_path}")
        return False

    # 查找模块
    modules = find_java_modules(project_path)
    if not modules:
        print("❌ 没有找到 Java 模块")
        return False

    print(f"📂 项目: {project_path.name}")
    print(f"📦 模块: {len(modules)} 个")

    # 配置 LLM
    llm_config = None
    if use_llm:
        api_key = api_key or os.getenv("OPENAI_API_KEY")
        if api_key:
            llm_config = LLMConfig(api_key=api_key)
            print("🤖 LLM 模式")
        else:
            print("⚠️  无 API 密钥，使用模板模式")

    # 生成文档
    generator = ACModGenerator(root_path=project_path, llm_config=llm_config)
    success_count = 0

    for i, module in enumerate(modules, 1):
        rel_path = module.relative_to(project_path) if module != project_path else "."
        print(f"[{i}/{len(modules)}] {rel_path}", end=" ")

        try:
            success = generator.generate_documentation(
                module_path=module,
                force=True,
                enable_context_limiting=True
            )

            if success:
                print("✅")
                success_count += 1
            else:
                print("❌")
        except Exception:
            print("❌")

    print(f"完成: {success_count}/{len(modules)}")
    return success_count > 0


def main():
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python gen_java_doc.py <java_project_root_path>")
        print("  python gen_java_doc.py <java_project_root_path> --llm")
        print("  python gen_java_doc.py <java_project_root_path> --llm --api-key your_key")
        print("\n功能: 为 Java 项目的所有模块生成 .ac.mod.md 文档")
        print("示例:")
        print("  python gen_java_doc.py /path/to/project")
        print("  python gen_java_doc.py /path/to/project --llm")
        sys.exit(1)

    project_path = sys.argv[1]
    use_llm = "--llm" in sys.argv

    # 获取 API 密钥
    api_key = None
    if "--api-key" in sys.argv:
        try:
            key_index = sys.argv.index("--api-key") + 1
            if key_index < len(sys.argv):
                api_key = sys.argv[key_index]
        except (ValueError, IndexError):
            pass

    success = generate_doc(project_path, use_llm, api_key)
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
