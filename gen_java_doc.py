#!/usr/bin/env python3
"""
简洁的 Java 项目文档生成脚本

使用方法: python gen_java_doc.py <java_project_path>
"""

import sys
from pathlib import Path
from generator import ACModGenerator


def generate_doc(project_path_str: str) -> bool:
    """生成 Java 项目文档"""
    project_path = Path(project_path_str).resolve()
    
    # 基本验证
    if not project_path.exists():
        print(f"❌ 路径不存在: {project_path}")
        return False
    
    if not project_path.is_dir():
        print(f"❌ 不是目录: {project_path}")
        return False
    
    # 检查 Java 文件
    java_files = list(project_path.rglob("*.java"))
    if not java_files:
        print("❌ 没有找到 Java 文件")
        return False
    
    print(f"📂 项目: {project_path.name}")
    print(f"📄 Java 文件: {len(java_files)} 个")
    
    try:
        # 生成文档
        generator = ACModGenerator(root_path=project_path.parent)
        success = generator.generate_documentation(
            module_path=project_path,
            force=True,
            enable_context_limiting=True
        )
        
        if success:
            doc_path = project_path / ".ac.mod.md"
            print(f"✅ 文档已生成: {doc_path}")
            return True
        else:
            print("❌ 生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False


def main():
    if len(sys.argv) != 2:
        print("使用方法: python gen_java_doc.py <java_project_path>")
        print("示例: python gen_java_doc.py /path/to/java/project")
        sys.exit(1)
    
    success = generate_doc(sys.argv[1])
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
