"""
Command Line Interface for AC Module Generator

This module provides a command-line interface for generating
.ac.mod.md files for Auto-Coder modules.
"""

import argparse
import logging
import sys
from pathlib import Path
from typing import Optional

try:
    from .generator import ACModGenerator
    from .context_limiter import ContextLimitConfig
    from .llm_config import LLMConfig
except ImportError:
    from generator import ACModGenerator
    from context_limiter import ContextLimitConfig
    from llm_config import LLMConfig


def setup_logging(verbose: bool = False):
    """Setup logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def generate_single_module(args):
    """Generate documentation for a single module."""
    module_path = Path(args.module_path).resolve()

    if not module_path.exists():
        print(f"Error: Module path does not exist: {module_path}")
        return 1

    if not module_path.is_dir():
        print(f"Error: Module path is not a directory: {module_path}")
        return 1

    # Determine root path
    root_path = Path(args.root_path).resolve() if args.root_path else module_path.parent

    # Create context config if specified
    context_config = None
    if hasattr(args, 'max_tokens') and args.max_tokens:
        context_config = ContextLimitConfig(
            max_tokens=args.max_tokens,
            include_test_files=getattr(args, 'include_tests', False)
        )

    # Create LLM config if specified
    llm_config = None
    if hasattr(args, 'api_base') and args.api_base and hasattr(args, 'api_key') and args.api_key:
        llm_config = LLMConfig(
            api_base=args.api_base,
            api_key=args.api_key,
            model=getattr(args, 'model', 'gpt-3.5-turbo')
        )

    generator = ACModGenerator(root_path, args.template, context_config, llm_config)

    # Check if hierarchical generation is requested
    if hasattr(args, 'hierarchical') and args.hierarchical:
        results = generator.generate_hierarchical_documentation(
            module_path, args.force,
            not getattr(args, 'disable_context_limiting', False)
        )

        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)

        print(f"Hierarchical generation complete: {success_count}/{total_count} successful")

        for module_path_str, success in results.items():
            status = "✓" if success else "✗"
            print(f"  {status} {module_path_str}")

        return 0 if success_count > 0 else 1

    # Use context limiting if enabled
    elif hasattr(args, 'enable_context_limiting') and args.enable_context_limiting:
        success = generator.generate_documentation_with_context_limiting(
            module_path, args.force, getattr(args, 'max_tokens', 32000)
        )
    else:
        success = generator.generate_documentation(module_path, args.force)

    if success:
        print(f"✓ Generated documentation for: {module_path}")
        return 0
    else:
        print(f"✗ Failed to generate documentation for: {module_path}")
        return 1


def generate_all_modules(args):
    """Generate documentation for all discovered modules."""
    root_path = Path(args.root_path).resolve() if args.root_path else Path.cwd()

    # Create LLM config if specified
    llm_config = None
    if hasattr(args, 'api_base') and args.api_base and hasattr(args, 'api_key') and args.api_key:
        llm_config = LLMConfig(
            api_base=args.api_base,
            api_key=args.api_key,
            model=getattr(args, 'model', 'gpt-3.5-turbo')
        )

    generator = ACModGenerator(root_path, args.template, None, llm_config)

    print(f"Scanning for modules in: {root_path}")

    # Check if hierarchical generation is requested
    if hasattr(args, 'hierarchical') and args.hierarchical:
        modules = generator.discover_modules(include_subpackages=False)  # Only main modules

        if not modules:
            print("No potential modules found.")
            return 0

        print(f"Found {len(modules)} main modules for hierarchical generation:")
        for module in modules:
            print(f"  - {module}")

        if not args.yes:
            response = input("\nProceed with hierarchical generation? (y/N): ")
            if response.lower() not in ['y', 'yes']:
                print("Cancelled.")
                return 0

        all_results = generator.generate_all_hierarchical_documentation(
            args.force, not getattr(args, 'disable_context_limiting', False)
        )

        # Calculate statistics
        total_main_modules = len(all_results)
        total_documents = sum(len(results) for results in all_results.values())
        successful_documents = sum(
            sum(1 for success in results.values() if success)
            for results in all_results.values()
        )

        print(f"\nHierarchical generation complete:")
        print(f"  Main modules: {total_main_modules}")
        print(f"  Total documents: {successful_documents}/{total_documents} successful")

        # Show detailed results
        for main_module, results in all_results.items():
            print(f"\n{main_module}:")
            for module_path, success in results.items():
                status = "✓" if success else "✗"
                rel_path = "  └─ " + str(Path(module_path).name) if module_path != main_module else "  ├─ (main)"
                print(f"    {status} {rel_path}")

        return 0 if successful_documents > 0 else 1

    else:
        # Standard generation
        modules = generator.discover_modules()

        if not modules:
            print("No potential modules found.")
            return 0

        print(f"Found {len(modules)} potential modules:")
        for module in modules:
            print(f"  - {module}")

        if not args.yes:
            response = input("\nProceed with generation? (y/N): ")
            if response.lower() not in ['y', 'yes']:
                print("Cancelled.")
                return 0

        results = generator.generate_all_documentation(args.force)

        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)

        print(f"\nGeneration complete: {success_count}/{total_count} successful")

        for module_path, success in results.items():
            status = "✓" if success else "✗"
            print(f"  {status} {module_path}")

        return 0 if success_count > 0 else 1


def update_module(args):
    """Update existing documentation for a module."""
    module_path = Path(args.module_path).resolve()
    
    if not module_path.exists():
        print(f"Error: Module path does not exist: {module_path}")
        return 1
    
    # Determine root path
    root_path = Path(args.root_path).resolve() if args.root_path else module_path.parent
    
    generator = ACModGenerator(root_path, args.template)
    
    success = generator.update_existing_documentation(module_path)
    
    if success:
        print(f"✓ Updated documentation for: {module_path}")
        return 0
    else:
        print(f"✗ Failed to update documentation for: {module_path}")
        return 1


def list_modules(args):
    """List all discovered modules."""
    root_path = Path(args.root_path).resolve() if args.root_path else Path.cwd()
    
    generator = ACModGenerator(root_path)
    modules = generator.discover_modules()
    
    print(f"Potential modules in {root_path}:")
    
    if not modules:
        print("  No modules found.")
        return 0
    
    for module in modules:
        doc_path = module / ".ac.mod.md"
        status = "📄" if doc_path.exists() else "❌"
        rel_path = module.relative_to(root_path) if module != root_path else "."
        print(f"  {status} {rel_path}")
    
    print(f"\nTotal: {len(modules)} modules")
    print("📄 = has .ac.mod.md, ❌ = missing .ac.mod.md")
    
    return 0


def check_context_limits(args):
    """Check context limits for a module without generating documentation."""
    module_path = Path(args.module_path).resolve()

    if not module_path.exists():
        print(f"Error: Module path does not exist: {module_path}")
        return 1

    if not module_path.is_dir():
        print(f"Error: Module path is not a directory: {module_path}")
        return 1

    # Determine root path
    root_path = Path(args.root_path).resolve() if args.root_path else module_path.parent

    generator = ACModGenerator(root_path)

    # Check context limits
    result = generator.check_module_context_limits(module_path, args.max_tokens)

    print(f"Context Analysis for: {module_path}")
    print("=" * 50)
    print(result["summary"])

    if result["exceeds_limit"]:
        print(f"\n⚠️  Module exceeds {args.max_tokens:,} token limit!")
        print(f"Recommended strategy: {result['recommended_strategy']}")

        if args.show_files:
            print("\nFile breakdown:")
            for file_info in result["file_breakdown"]:
                if "error" not in file_info:
                    print(f"  {file_info['tokens']:>6} tokens - {file_info['path']}")
    else:
        print(f"\n✓ Module is within {args.max_tokens:,} token limit")

    return 0


def main():
    """Main entry point for the CLI."""
    parser = argparse.ArgumentParser(
        description="Generate .ac.mod.md documentation for Auto-Coder modules",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Generate documentation for a specific module
  ac-mod-gen generate ./src/my_module

  # Generate hierarchical documentation (main module + subpackages)
  ac-mod-gen generate ./src/large_java_module --hierarchical

  # Generate documentation with context limiting for large Java modules
  ac-mod-gen generate ./src/large_java_module --enable-context-limiting --max-tokens 24000

  # Generate documentation using LLM (AI-powered)
  ac-mod-gen generate ./src/my_module --api-base https://api.openai.com/v1 --api-key your_api_key

  # Generate hierarchical documentation for all modules
  ac-mod-gen generate-all --hierarchical

  # Check if a module exceeds context limits
  ac-mod-gen check-context ./src/my_module --show-files

  # Generate documentation for all modules in current directory
  ac-mod-gen generate-all

  # List all potential modules
  ac-mod-gen list

  # Update existing documentation
  ac-mod-gen update ./src/my_module

  # Use custom template
  ac-mod-gen generate ./src/my_module --template ./my_template.md
        """
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )
    
    parser.add_argument(
        '--root-path', '-r',
        help='Root path for the project (default: current directory or module parent)'
    )
    
    parser.add_argument(
        '--template', '-t',
        help='Path to custom template file'
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Generate command
    generate_parser = subparsers.add_parser(
        'generate',
        help='Generate documentation for a specific module'
    )
    generate_parser.add_argument(
        'module_path',
        help='Path to the module directory'
    )
    generate_parser.add_argument(
        '--force', '-f',
        action='store_true',
        help='Overwrite existing documentation'
    )
    generate_parser.add_argument(
        '--enable-context-limiting',
        action='store_true',
        help='Enable context limiting for large modules'
    )
    generate_parser.add_argument(
        '--max-tokens',
        type=int,
        default=32000,
        help='Maximum token limit for context (default: 32000)'
    )
    generate_parser.add_argument(
        '--include-tests',
        action='store_true',
        help='Include test files in analysis'
    )
    generate_parser.add_argument(
        '--hierarchical',
        action='store_true',
        help='Generate hierarchical documentation (main module + subpackages)'
    )
    generate_parser.add_argument(
        '--disable-context-limiting',
        action='store_true',
        help='Disable context limiting even for large modules'
    )
    generate_parser.add_argument(
        '--api-base',
        help='LLM API base URL (e.g., https://api.openai.com/v1)'
    )
    generate_parser.add_argument(
        '--api-key',
        help='LLM API key for authentication'
    )
    generate_parser.add_argument(
        '--model',
        default='gpt-3.5-turbo',
        help='LLM model name to use (default: gpt-3.5-turbo)'
    )
    
    # Generate-all command
    generate_all_parser = subparsers.add_parser(
        'generate-all',
        help='Generate documentation for all discovered modules'
    )
    generate_all_parser.add_argument(
        '--force', '-f',
        action='store_true',
        help='Overwrite existing documentation'
    )
    generate_all_parser.add_argument(
        '--yes', '-y',
        action='store_true',
        help='Skip confirmation prompt'
    )
    generate_all_parser.add_argument(
        '--api-base',
        help='LLM API base URL (e.g., https://api.openai.com/v1)'
    )
    generate_all_parser.add_argument(
        '--api-key',
        help='LLM API key for authentication'
    )
    generate_all_parser.add_argument(
        '--model',
        default='gpt-3.5-turbo',
        help='LLM model name to use (default: gpt-3.5-turbo)'
    )
    generate_all_parser.add_argument(
        '--hierarchical',
        action='store_true',
        help='Generate hierarchical documentation for all modules (main + subpackages)'
    )
    generate_all_parser.add_argument(
        '--disable-context-limiting',
        action='store_true',
        help='Disable context limiting even for large modules'
    )
    
    # Update command
    update_parser = subparsers.add_parser(
        'update',
        help='Update existing documentation for a module'
    )
    update_parser.add_argument(
        'module_path',
        help='Path to the module directory'
    )
    
    # List command
    list_parser = subparsers.add_parser(
        'list',
        help='List all potential modules'
    )

    # Check context limits command
    check_parser = subparsers.add_parser(
        'check-context',
        help='Check context limits for a module'
    )
    check_parser.add_argument(
        'module_path',
        help='Path to the module directory'
    )
    check_parser.add_argument(
        '--max-tokens',
        type=int,
        default=32000,
        help='Maximum token limit to check against (default: 32000)'
    )
    check_parser.add_argument(
        '--show-files',
        action='store_true',
        help='Show detailed file breakdown'
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.verbose)
    
    # Execute command
    if args.command == 'generate':
        return generate_single_module(args)
    elif args.command == 'generate-all':
        return generate_all_modules(args)
    elif args.command == 'update':
        return update_module(args)
    elif args.command == 'list':
        return list_modules(args)
    elif args.command == 'check-context':
        return check_context_limits(args)
    else:
        parser.print_help()
        return 1


if __name__ == '__main__':
    sys.exit(main())
