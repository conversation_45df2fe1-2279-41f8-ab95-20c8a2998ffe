#!/usr/bin/env python3
"""
Test LLM Integration for AC Module Generator

This script tests the LLM integration functionality.
"""

import os
import tempfile
import unittest
from pathlib import Path
from unittest.mock import Mock, patch

from llm_config import LLMConfig, LLMClient
from generator import ACModGenerator


class TestLLMConfig(unittest.TestCase):
    """Test LLM configuration functionality"""
    
    def test_default_config(self):
        """Test default configuration"""
        config = LLMConfig()
        self.assertEqual(config.api_base, "https://api.openai.com/v1")
        self.assertEqual(config.model, "gpt-3.5-turbo")
        self.assertEqual(config.max_tokens, 4000)
        self.assertEqual(config.temperature, 0.1)
    
    def test_custom_config(self):
        """Test custom configuration"""
        config = LLMConfig(
            api_base="http://localhost:8000/v1",
            api_key="test_key",
            model="llama2",
            max_tokens=2000,
            temperature=0.5
        )
        self.assertEqual(config.api_base, "http://localhost:8000/v1")
        self.assertEqual(config.api_key, "test_key")
        self.assertEqual(config.model, "llama2")
        self.assertEqual(config.max_tokens, 2000)
        self.assertEqual(config.temperature, 0.5)
    
    def test_environment_variables(self):
        """Test configuration from environment variables"""
        with patch.dict(os.environ, {
            'OPENAI_API_BASE': 'http://test.com/v1',
            'OPENAI_API_KEY': 'env_key',
            'OPENAI_MODEL': 'gpt-4',
            'OPENAI_MAX_TOKENS': '8000',
            'OPENAI_TEMPERATURE': '0.2'
        }):
            config = LLMConfig()
            self.assertEqual(config.api_base, "http://test.com/v1")
            self.assertEqual(config.api_key, "env_key")
            self.assertEqual(config.model, "gpt-4")
            self.assertEqual(config.max_tokens, 8000)
            self.assertEqual(config.temperature, 0.2)


class TestLLMClient(unittest.TestCase):
    """Test LLM client functionality"""
    
    def test_client_without_api_key(self):
        """Test client behavior without API key"""
        config = LLMConfig(api_key=None)
        client = LLMClient(config)
        self.assertFalse(client.is_available())
    
    def test_client_with_api_key(self):
        """Test client behavior with API key"""
        config = LLMConfig(api_key="test_key")
        client = LLMClient(config)
        self.assertTrue(client.is_available())
    
    @patch('llm_config.openai.OpenAI')
    def test_chat_completion_success(self, mock_openai):
        """Test successful chat completion"""
        # Mock OpenAI response
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "Generated documentation"
        
        mock_client = Mock()
        mock_client.chat.completions.create.return_value = mock_response
        mock_openai.return_value = mock_client
        
        config = LLMConfig(api_key="test_key")
        client = LLMClient(config)
        
        messages = [{"role": "user", "content": "Generate docs"}]
        result = client.chat_completion(messages)
        
        self.assertEqual(result, "Generated documentation")
        mock_client.chat.completions.create.assert_called_once()
    
    @patch('llm_config.openai.OpenAI')
    def test_chat_completion_failure(self, mock_openai):
        """Test chat completion failure"""
        mock_openai.side_effect = Exception("API Error")
        
        config = LLMConfig(api_key="test_key")
        client = LLMClient(config)
        
        messages = [{"role": "user", "content": "Generate docs"}]
        result = client.chat_completion(messages)
        
        self.assertIsNone(result)
    
    def test_generate_documentation_without_api_key(self):
        """Test documentation generation without API key"""
        config = LLMConfig(api_key=None)
        client = LLMClient(config)
        
        module_info = {"name": "test_module", "path": "./test"}
        result = client.generate_documentation(module_info)
        
        self.assertIsNone(result)


class TestACModGeneratorLLMIntegration(unittest.TestCase):
    """Test AC Module Generator LLM integration"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_module_path = Path(self.temp_dir) / "test_module"
        self.test_module_path.mkdir()
        
        # Create a simple Python file
        (self.test_module_path / "__init__.py").write_text(
            '"""Test module for LLM integration"""\n'
        )
        (self.test_module_path / "main.py").write_text(
            'def hello():\n    """Say hello"""\n    return "Hello, World!"\n'
        )
    
    def test_generator_without_llm_config(self):
        """Test generator without LLM configuration"""
        generator = ACModGenerator(root_path=self.temp_dir)
        self.assertFalse(generator.llm_client.is_available())
    
    def test_generator_with_llm_config(self):
        """Test generator with LLM configuration"""
        llm_config = LLMConfig(api_key="test_key")
        generator = ACModGenerator(
            root_path=self.temp_dir,
            llm_config=llm_config
        )
        self.assertTrue(generator.llm_client.is_available())
    
    def test_set_llm_config_runtime(self):
        """Test setting LLM config at runtime"""
        generator = ACModGenerator(root_path=self.temp_dir)
        self.assertFalse(generator.llm_client.is_available())
        
        generator.set_llm_config(
            api_base="https://api.openai.com/v1",
            api_key="test_key",
            model="gpt-3.5-turbo"
        )
        self.assertTrue(generator.llm_client.is_available())
    
    @patch.object(LLMClient, 'generate_documentation')
    def test_generate_content_with_llm(self, mock_generate):
        """Test content generation with LLM"""
        mock_generate.return_value = "# LLM Generated Documentation\n\nThis is AI-generated content."
        
        llm_config = LLMConfig(api_key="test_key")
        generator = ACModGenerator(
            root_path=self.temp_dir,
            llm_config=llm_config
        )
        
        analysis = {
            "name": "test_module",
            "path": "./test_module",
            "description": "Test module"
        }
        
        content = generator._generate_content(analysis)
        self.assertIn("LLM Generated Documentation", content)
        mock_generate.assert_called_once_with(analysis)
    
    @patch.object(LLMClient, 'generate_documentation')
    def test_generate_content_fallback_to_template(self, mock_generate):
        """Test fallback to template when LLM fails"""
        mock_generate.return_value = None  # LLM generation failed
        
        llm_config = LLMConfig(api_key="test_key")
        generator = ACModGenerator(
            root_path=self.temp_dir,
            llm_config=llm_config
        )
        
        analysis = {
            "name": "test_module",
            "path": "./test_module",
            "description": "Test module",
            "directory_structure": {},
            "core_components": [],
            "dependencies": [],
            "usage_examples": [],
            "test_commands": []
        }
        
        content = generator._generate_content(analysis)
        # Should contain template-generated content
        self.assertIn("test_module", content)
        mock_generate.assert_called_once_with(analysis)
    
    def test_generate_content_without_llm(self):
        """Test content generation without LLM configuration"""
        generator = ACModGenerator(root_path=self.temp_dir)
        
        analysis = {
            "name": "test_module",
            "path": "./test_module",
            "description": "Test module",
            "directory_structure": {},
            "core_components": [],
            "dependencies": [],
            "usage_examples": [],
            "test_commands": []
        }
        
        content = generator._generate_content(analysis)
        # Should contain template-generated content
        self.assertIn("test_module", content)


class TestPromptGeneration(unittest.TestCase):
    """Test prompt generation for LLM"""
    
    def test_create_documentation_prompt(self):
        """Test documentation prompt creation"""
        config = LLMConfig(api_key="test_key")
        client = LLMClient(config)
        
        module_info = {
            "name": "test_module",
            "path": "./test_module",
            "description": "A test module for demonstration",
            "directory_structure": "test_module/\n  __init__.py\n  main.py",
            "core_components": [
                {"name": "TestClass", "type": "class", "description": "Main test class"}
            ],
            "dependencies": ["other_module"]
        }
        
        prompt = client._create_documentation_prompt(module_info)
        
        self.assertIn("test_module", prompt)
        self.assertIn("A test module for demonstration", prompt)
        self.assertIn("TestClass", prompt)
        self.assertIn("other_module", prompt)
        self.assertIn("请为以下模块生成", prompt)


if __name__ == "__main__":
    unittest.main()
