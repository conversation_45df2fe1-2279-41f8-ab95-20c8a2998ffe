#!/usr/bin/env python3
"""
Java 项目文档生成测试脚本

使用方法:
    python test_java_project.py /path/to/your/java/project
    
或者直接修改脚本中的 JAVA_PROJECT_PATH 变量
"""

import sys
import os
from pathlib import Path
from generator import ACModGenerator
from llm_config import LLMConfig


# 配置区域 - 您可以直接修改这些值
JAVA_PROJECT_PATH = "/path/to/your/java/project"  # 修改为您的 Java 项目路径

# LLM 配置 (可选)
USE_LLM = False  # 设置为 True 启用 LLM 生成
LLM_API_BASE = "https://api.openai.com/v1"
LLM_API_KEY = "your_api_key_here"  # 替换为您的 API 密钥
LLM_MODEL = "gpt-3.5-turbo"


def validate_java_project(project_path: Path) -> bool:
    """
    验证是否为有效的 Java 项目
    
    Args:
        project_path: 项目路径
        
    Returns:
        是否为有效的 Java 项目
    """
    if not project_path.exists():
        print(f"❌ 路径不存在: {project_path}")
        return False
    
    if not project_path.is_dir():
        print(f"❌ 不是目录: {project_path}")
        return False
    
    # 检查是否包含 Java 文件
    java_files = list(project_path.rglob("*.java"))
    if not java_files:
        print(f"❌ 目录中没有找到 Java 文件: {project_path}")
        return False
    
    print(f"✅ 找到 {len(java_files)} 个 Java 文件")
    
    # 检查是否为 Maven 项目
    if (project_path / "pom.xml").exists():
        print("✅ 检测到 Maven 项目 (pom.xml)")
    
    # 检查是否为 Gradle 项目
    if (project_path / "build.gradle").exists() or (project_path / "build.gradle.kts").exists():
        print("✅ 检测到 Gradle 项目")
    
    return True


def analyze_project_structure(project_path: Path):
    """
    分析项目结构
    
    Args:
        project_path: 项目路径
    """
    print("\n" + "=" * 60)
    print("项目结构分析")
    print("=" * 60)
    
    # 统计文件类型
    java_files = list(project_path.rglob("*.java"))
    xml_files = list(project_path.rglob("*.xml"))
    properties_files = list(project_path.rglob("*.properties"))
    
    print(f"Java 文件: {len(java_files)} 个")
    print(f"XML 文件: {len(xml_files)} 个")
    print(f"Properties 文件: {len(properties_files)} 个")
    
    # 显示主要目录结构
    print("\n主要目录:")
    for item in sorted(project_path.iterdir()):
        if item.is_dir() and not item.name.startswith('.'):
            java_count = len(list(item.rglob("*.java")))
            if java_count > 0:
                print(f"  📁 {item.name}/ ({java_count} Java 文件)")
    
    # 显示一些示例 Java 文件
    print(f"\n示例 Java 文件 (前5个):")
    for i, java_file in enumerate(java_files[:5]):
        rel_path = java_file.relative_to(project_path)
        print(f"  📄 {rel_path}")


def setup_llm_config() -> LLMConfig:
    """
    设置 LLM 配置
    
    Returns:
        LLM 配置对象，如果不使用 LLM 则返回 None
    """
    if not USE_LLM:
        print("📝 使用模板生成模式")
        return None
    
    if LLM_API_KEY == "your_api_key_here":
        print("⚠️  LLM API 密钥未配置，将使用模板生成")
        return None
    
    print("🤖 使用 LLM 生成模式")
    return LLMConfig(
        api_base=LLM_API_BASE,
        api_key=LLM_API_KEY,
        model=LLM_MODEL
    )


def generate_documentation(project_path: Path, llm_config: LLMConfig = None):
    """
    生成文档
    
    Args:
        project_path: 项目路径
        llm_config: LLM 配置
    """
    print("\n" + "=" * 60)
    print("开始生成文档")
    print("=" * 60)
    
    try:
        # 创建生成器
        generator = ACModGenerator(
            root_path=project_path.parent,
            llm_config=llm_config
        )
        
        # 检查上下文限制
        print("🔍 检查模块上下文限制...")
        context_result = generator.check_module_context_limits(project_path)
        
        print(f"总文件数: {context_result['total_files']}")
        print(f"总 Token 数: {context_result['total_tokens']:,}")
        print(f"超过限制: {'是' if context_result['exceeds_limit'] else '否'}")
        
        if context_result['exceeds_limit']:
            print(f"推荐策略: {context_result['recommended_strategy']}")
            print("⚠️  项目较大，将启用上下文限制功能")
        
        # 生成文档
        print("\n📝 开始生成文档...")
        success = generator.generate_documentation(
            project_path, 
            force=True,  # 强制覆盖现有文档
            enable_context_limiting=context_result['exceeds_limit']
        )
        
        if success:
            doc_path = project_path / ".ac.mod.md"
            print(f"✅ 文档生成成功!")
            print(f"📄 文档位置: {doc_path}")
            
            # 显示文档预览
            if doc_path.exists():
                content = doc_path.read_text(encoding='utf-8')
                lines = content.split('\n')
                print(f"\n📖 文档预览 (前10行):")
                print("-" * 40)
                for i, line in enumerate(lines[:10], 1):
                    print(f"{i:2d}: {line}")
                if len(lines) > 10:
                    print(f"... (还有 {len(lines) - 10} 行)")
                
                print(f"\n📊 文档统计:")
                print(f"  总行数: {len(lines)}")
                print(f"  总字符数: {len(content)}")
        else:
            print("❌ 文档生成失败")
            
    except Exception as e:
        print(f"❌ 生成过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("Java 项目文档生成测试脚本")
    print("=" * 60)
    
    # 获取项目路径
    if len(sys.argv) > 1:
        project_path = Path(sys.argv[1]).resolve()
        print(f"📂 使用命令行参数指定的路径: {project_path}")
    else:
        project_path = Path(JAVA_PROJECT_PATH).resolve()
        print(f"📂 使用脚本中配置的路径: {project_path}")
        
        if str(project_path) == "/path/to/your/java/project":
            print("\n⚠️  请修改脚本中的 JAVA_PROJECT_PATH 变量或使用命令行参数指定路径")
            print("使用方法:")
            print(f"  python {sys.argv[0]} /path/to/your/java/project")
            return
    
    # 验证项目
    if not validate_java_project(project_path):
        return
    
    # 分析项目结构
    analyze_project_structure(project_path)
    
    # 设置 LLM 配置
    llm_config = setup_llm_config()
    
    # 生成文档
    generate_documentation(project_path, llm_config)
    
    print("\n" + "=" * 60)
    print("测试完成!")
    print("=" * 60)


if __name__ == "__main__":
    main()
