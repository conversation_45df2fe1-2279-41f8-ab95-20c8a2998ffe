#!/usr/bin/env python3
"""
快速 Java 项目文档生成脚本

简单易用的一键生成脚本
"""

import sys
from pathlib import Path
from generator import ACModGenerator


def quick_generate(java_project_path: str):
    """
    快速生成 Java 项目文档
    
    Args:
        java_project_path: Java 项目路径
    """
    project_path = Path(java_project_path).resolve()
    
    print(f"🚀 开始为 Java 项目生成文档")
    print(f"📂 项目路径: {project_path}")
    
    # 基本验证
    if not project_path.exists():
        print(f"❌ 路径不存在: {project_path}")
        return False
    
    if not project_path.is_dir():
        print(f"❌ 不是目录: {project_path}")
        return False
    
    # 检查 Java 文件
    java_files = list(project_path.rglob("*.java"))
    if not java_files:
        print(f"❌ 没有找到 Java 文件")
        return False
    
    print(f"✅ 找到 {len(java_files)} 个 Java 文件")
    
    try:
        # 创建生成器
        generator = ACModGenerator(root_path=project_path.parent)
        
        # 生成文档
        print("📝 正在生成文档...")
        success = generator.generate_documentation(
            project_path, 
            force=True,
            enable_context_limiting=True  # 自动启用上下文限制
        )
        
        if success:
            doc_path = project_path / ".ac.mod.md"
            print(f"✅ 文档生成成功!")
            print(f"📄 文档位置: {doc_path}")
            return True
        else:
            print("❌ 文档生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False


def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法:")
        print(f"  python {sys.argv[0]} <java_project_path>")
        print("\n示例:")
        print(f"  python {sys.argv[0]} /path/to/your/java/project")
        print(f"  python {sys.argv[0]} ./my-java-app")
        return
    
    java_project_path = sys.argv[1]
    success = quick_generate(java_project_path)
    
    if success:
        print("\n🎉 完成! 您的 Java 项目文档已生成")
    else:
        print("\n💥 生成失败，请检查路径和项目结构")


if __name__ == "__main__":
    main()
